syntax = "proto3";

package api.typesv2.deeplink_screen_option.onboarding;

import "api/typesv2/common/text.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/frontend/deeplink/deeplink.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.onboarding";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// screen options for SKIP_VERIFY_CKYC_OTP_BOTTOMSHEET
// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=4167-3091&t=2e8wDvmNndkaTVuE-0
message SkipCKYCOtpBottomSheetScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.Text title = 2;
  DeclarationCheckboxContent declaration = 3;
  repeated frontend.deeplink.Cta ctas = 4;
}

message DeclarationCheckboxContent {
  typesv2.common.Text declaration_title = 1;
  repeated Consent consents = 2;
}

message Consent {
  typesv2.common.Text consent = 1;
  // BE will send this consent list and client will send back to BE after user give consent.
  repeated string consent_list = 2;
  bool is_mandatory = 3;
}
