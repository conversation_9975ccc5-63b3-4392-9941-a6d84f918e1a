syntax = "proto3";

package api.typesv2.deeplink_screen_option.onboarding;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.onboarding";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// screen options for VERIFY_CKYC_OTP_BOTTOMSHEET
// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=4167-3091&t=2e8wDvmNndkaTVuE-0
message CKYCOtpBottomSheetScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // BE sends contents like image, title etc.
  OtpScreenContents screen_contents = 2;
}

message  OtpScreenContents {
  typesv2.common.VisualElement title_image = 1;
  typesv2.common.Text title = 2;
  typesv2.common.Text subtitle = 3;
  typesv2.ui.IconTextComponent cta_header = 4;
  repeated frontend.deeplink.Cta ctas = 5;
}
