syntax = "proto3";

package vendorgateway.lending.preapprovedloan.lenden;

import "api/rpc/status.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/bank_account_details.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/lending/preapprovedloan/lenden/enums.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendors/lenden/get_loan_details.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/decimal.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.preapprovedloan.lenden";

service Lenden {
  rpc CreateUser (CreateUserRequest) returns (CreateUserResponse);
  rpc ApplyForLoan (ApplyForLoanRequest) returns (ApplyForLoanResponse);
  rpc CheckHardEligibility (CheckHardEligibilityRequest) returns (CheckHardEligibilityResponse);
  rpc SelectOffer (SelectOfferRequest) returns (SelectOfferResponse);
  rpc ModifyRateOfInterest (ModifyRateOfInterestRequest) returns (ModifyRateOfInterestResponse);
  rpc KycInit (KycInitRequest) returns (KycInitResponse);
  rpc CheckKycStatus (CheckKycStatusRequest) returns (CheckKycStatusResponse);
  rpc AddBankDetails (AddBankDetailsRequest) returns (AddBankDetailsResponse);
  rpc InitMandate (InitMandateRequest) returns (InitMandateResponse);
  rpc CheckMandateStatus (CheckMandateStatusRequest) returns (CheckMandateStatusResponse);
  rpc GenerateKfsLa (GenerateKfsLaRequest) returns (GenerateKfsLaResponse);
  rpc SignKfsLa (SignKfsLaRequest) returns (SignKfsLaResponse);
  rpc GetLoanDetails (GetLoanDetailsRequest) returns (GetLoanDetailsResponse);
  rpc GetPreDisbursementDetails (GetPreDisbursementDetailsRequest) returns (GetPreDisbursementDetailsResponse);
  rpc PostExternalData (PostExternalDataRequest) returns (PostExternalDataResponse);
  rpc GetAmortizationSchedule (GetAmortizationScheduleRequest) returns (GetAmortizationScheduleResponse);

  // GetForeclosureDetails returns details like the amount needed to close a loan before its due date
  // with breakup of principal, interest, foreclosure charges etc.
  // RBI guidelines require lenders to allow users to cancel the loan in the first few days post disbursal
  // by paying back the disbursed amount without any interest charges. This period is also called as the cool-off period.
  // When a loan is in cool-off period, GetForeclosureDetails should be called with purpose as PURPOSE_COOL_OFF
  // to get the amount needed to cancel the loan. Typically, in this period the foreclosure charges will be 0.
  rpc GetForeclosureDetails (GetForeclosureDetailsRequest) returns (GetForeclosureDetailsResponse);

  rpc GeneratePaymentLink (GeneratePaymentLinkRequest) returns (GeneratePaymentLinkResponse);
  rpc GetPaymentStatus (GetPaymentStatusRequest) returns (GetPaymentStatusResponse);
}

message CreateUserRequest {
  vendorgateway.RequestHeader header = 1;

  api.typesv2.common.Name name = 2 [(validate.rules).message = {required: true}];
  string pan = 3 [(validate.rules).string.min_len = 1];
  api.typesv2.common.PhoneNumber phone_number = 4 [(validate.rules).message = {required: true}];
  string email = 5 [(validate.rules).string.email = true];
  google.type.Date dob = 6 [(validate.rules).message = {required: true}];
  // this denotes a unique identifier for the user creation request for idempotency purposes
  string reference_id = 7 [(validate.rules).string.min_len = 1];
  // ONLY VALID for Salaried and Self Employed
  LendenEmploymentType employment_type = 8 [(validate.rules).enum = {not_in: [0]}];
  // Name of the org where the user is employed
  string employment_organization_name = 9;
  // List of consent codes that the user has consented to
  repeated ConsentType consent_type_list = 10;
  // denotes the user's device ip from which they are applying for the loan.
  string user_ip = 11 [(validate.rules).string.ip = true];
  // denotes the user's device id from which they are applying for the loan.
  string device_id = 12 [(validate.rules).string.min_len = 1];
  // [Optional] latitude and longitude of the user from where they are applying for the loan.
  string latitude = 13;
  string longitude = 14;
  // denotes the time when the user has given consent
  google.protobuf.Timestamp consent_time = 15 [(validate.rules).timestamp.required = true];
}

message CreateUserResponse {
  rpc.Status status = 1;
  // denotes a unique id of a user in LDC system.
  string user_id = 2;
}

message ApplyForLoanRequest {
  vendorgateway.RequestHeader header = 1;

  // unique id of the user in LDC system
  string user_id = 2 [(validate.rules).string.min_len = 1];
  // this denotes a unique identifier for the user creation request for idempotency purposes
  string reference_id = 3 [(validate.rules).string.min_len = 1];
  // denotes the amount of loan requested by the user
  google.type.Money requested_amount = 4;
  Interest interest = 5 [(validate.rules).message = {required: true}];
  AddressType address_type = 6 [(validate.rules).enum = {not_in: [0]}];
  // Address of the user accepting the loan
  // [Required] state in address.administrative_area
  // [Required] pincode in address.postal_code
  // [Required] address in address.address_lines
  api.typesv2.common.PostalAddress address = 7 [(validate.rules).message = {required: true}];

  message Interest {
    InterestType type = 1 [(validate.rules).enum = {not_in: [0]}];
    InterestFrequency frequency = 2 [(validate.rules).enum = {not_in: [0]}];
    float value = 3 [(validate.rules).float.gt = 0];
  }
}

message ApplyForLoanResponse {
  enum Status {
    OK = 0;
    MAX_LOAN_ACCOUNT_REACHED_AT_VENDOR = 101;
  }
  rpc.Status status = 1;
  string loan_id = 2;
}

message CheckHardEligibilityRequest {
  vendorgateway.RequestHeader header = 1;
  // Loan Id to check hard Eligibility
  string loan_id = 2 [(validate.rules).string.min_len = 1];
  // LDC user_id
  string user_id = 3 [(validate.rules).string.min_len = 1];
}

message CheckHardEligibilityResponse {
  rpc.Status status = 1;
  // denotes the eligibility status of the user for the loan
  EligibilityStatus eligibility_status = 2;
  // possible offer details
  OfferData offer_data = 3;
}

message OfferData {
  message ApplicableAmountToTenure {
    google.type.Money min_amount = 1;
    google.type.Money max_amount = 2;
    int32 min_tenure = 3;
    int32 max_tenure = 4;
  }
  repeated P2pOfferDetails offers = 1;
  int32 offer_selection_multiple = 2;
  google.type.Money min_amount = 3;
  google.type.Money max_amount = 4;
  repeated ApplicableAmountToTenure applicable_tenures = 5;
}

message P2pOfferDetails {
  string offer_code = 1;
  double roi = 2;
  string funding_probability = 3;
  string expected_time_to_get_funding = 4;
  bool is_recommended = 5;
  repeated double modify_roi_list = 6;
}

message SelectOfferRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2 [(validate.rules).string.min_len = 1];
  string user_id = 3 [(validate.rules).string.min_len = 1];
  google.type.Money selected_amount = 4 [(validate.rules).message = {required: true}];
  int32 tenure = 5;
  // denoted the selected offer id from the list of offers
  string selected_offer_id = 6 [(validate.rules).string.min_len = 1];
}

message SelectOfferResponse {
  rpc.Status status = 1;

  // ID / code of the offer selected in current or a previous API call
  string offer_code = 2;
}

message ModifyRateOfInterestRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2 [(validate.rules).string.min_len = 1];
  float interest_rate = 3 [(validate.rules).float.gt = 0];
  // List of consent codes that the user has consented to, Only accepted MODIFY_ROI
  repeated ConsentType consent_code_list = 4;
  // denotes the user's device ip from which they are applying for the loan.
  string user_ip = 5 [(validate.rules).string.ip = true];
  // denotes the user's device id from which they are applying for the loan.
  string device_id = 6 [(validate.rules).string.min_len = 1];
  // [Optional] latitude and longitude of the user from where they are applying for the loan.
  string latitude = 7;
  string longitude = 8;
  string user_id = 9;

  // Timestamp at which user consented to modifying ROI
  google.protobuf.Timestamp consented_at = 10;
}

message ModifyRateOfInterestResponse {
  rpc.Status status = 1;
  google.type.Money installment_amount = 2;
  string kfs_doc_url = 3;
  string loan_agreement_doc_url = 4;
}

message KycInitRequest {
  vendorgateway.RequestHeader header = 1;
  string user_id = 2 [(validate.rules).string.min_len = 1];
  string loan_id = 3 [(validate.rules).string.min_len = 1];
  // List of consent codes that the user has consented to
  repeated ConsentType consent_code_list = 4;
  // denotes the user's device ip from which they are applying for the loan.
  string user_ip = 5 [(validate.rules).string.ip = true];
  // denotes the user's device id from which they are applying for the loan.
  string device_id = 6 [(validate.rules).string.min_len = 1];
  // [Optional] latitude and longitude of the user from where they are applying for the loan.
  string latitude = 7;
  string longitude = 8;
  // denotes the time when the user has given consent
  google.protobuf.Timestamp consent_time = 15 [(validate.rules).timestamp.required = true];
}

message KycInitResponse {
  rpc.Status status = 1;
  KYCStatus kyc_status = 2;
  string redirection_url = 3;
  // useful for tracking the status of kyc setup
  string tracking_id = 4;
  string kyc_message = 5;
}

message CheckKycStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string user_id = 2 [(validate.rules).string.min_len = 1];
  // The tracking_id that is present in KycInitResponse
  string tracking_id = 3 [(validate.rules).string.min_len = 1];
}

message CheckKycStatusResponse {
  rpc.Status status = 1;
  KYCStatus kyc_status = 2;
}

enum KYCStatus {
  KYC_STATUS_UNSPECIFIED = 0;

  // A web link has been generated to complete the KYC process.
  KYC_STATUS_INITIATED = 1;

  // The KYC link has been opened and thus the KYC process is in progress.
  KYC_STATUS_IN_PROGRESS = 2;

  // KYC process has been completed successfully.
  KYC_STATUS_COMPLETED = 3;

  // KYC was not completed successfully, and the process should be re-initiated.
  KYC_STATUS_FAILED = 4;

  // The KYC link has expired and a new web link needs to be generated by re-initiating the KYC process.
  KYC_STATUS_EXPIRED = 5;
}

message AddBankDetailsRequest {
  vendorgateway.RequestHeader header = 1;

  string user_id = 2 [(validate.rules).string.min_len = 1];
  string loan_id = 3 [(validate.rules).string.min_len = 1];
  // Only supported Account Type is Savings or Current Account
  // BankAccountDetails.bank_name is optional
  api.typesv2.common.BankAccountDetails bank_account_details = 4 [(validate.rules).message = {required: true}];
}

message AddBankDetailsResponse {
  enum Status {
    OK = 0;
    BANK_ACCOUNT_NOT_ACTIVE = 101;
    NAME_MISMATCH = 102;
    BANK_CONNECTION_ERROR = 103;
    INVALID_BANK_DETAILS = 104;
  }
  rpc.Status status = 1;
}

message InitMandateRequest {
  vendorgateway.RequestHeader header = 1;
  string user_id = 2 [(validate.rules).string.min_len = 1];
  string loan_id = 3 [(validate.rules).string.min_len = 1];
  // Mandate Type "NACH_MANDATE" / "UPI_MANDATE",
  MandateType mandate_type = 4 [(validate.rules).enum = {not_in: [0]}];
  // List of consent codes that the user has consented to
  repeated ConsentType consent_code_list = 5;
  // denotes the user's device ip from which they are applying for the loan.
  string user_ip = 6 [(validate.rules).string.ip = true];
  // denotes the user's device id from which they are applying for the loan.
  string device_id = 7 [(validate.rules).string.min_len = 1];
  // [Optional] latitude and longitude of the user from where they are applying for the loan.
  string latitude = 8;
  string longitude = 9;
  // denotes the time when the user has given consent
  google.protobuf.Timestamp consent_time = 15 [(validate.rules).timestamp.required = true];
}

message InitMandateResponse {
  enum Status {
    OK = 0;
    INVALID_LOAN_STATUS = 101;
    MANDATE_VERIFICATION_FAILED = 102;
    OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND = 103;
    BANK_ACCOUNT_NOT_VERIFIED = 104;
    ENACH_ALREADY_COMPLETED = 105;
  }
  rpc.Status status = 1;
  string redirection_url = 2;
  // useful for tracking the status of mandate setup
  string tracking_id = 3;
  google.protobuf.Timestamp mandate_url_validity = 4;
  int64 mandate_amount = 5;
  // UMRN (Unique Mandate Reference Number) is a unique identifier generated by NPCI
  // when an e-NACH mandate is successfully registered. This field will be populated
  // when the mandate is already completed.
  string umrn = 6;
}

message CheckMandateStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string user_id = 2 [(validate.rules).string.min_len = 1];
  // Mandate Type "NACH_MANDATE" / "UPI_MANDATE",
  MandateType mandate_type = 3 [(validate.rules).enum = {not_in: [0]}];
  // The tracking_id that is present in InitMandateResponse
  string tracking_id = 4 [(validate.rules).string.min_len = 1];
}

message CheckMandateStatusResponse {
  rpc.Status status = 1;
  MandateStatus mandate_status = 2;
  string tracking_id = 3;
  google.protobuf.Timestamp completed_at = 4;
}

enum MandateStatus {
  MANDATE_STATUS_UNSPECIFIED = 0;

  MANDATE_STATUS_IN_PROGRESS = 1;

  MANDATE_STATUS_COMPLETED = 2;

  MANDATE_STATUS_FAILED = 3;

  MANDATE_STATUS_EXPIRED = 4;
}

message GenerateKfsLaRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2 [(validate.rules).string.min_len = 1];
  // User id of the user who is signing the KFS LA
  string user_id = 3 [(validate.rules).string.min_len = 1];
}

message GenerateKfsLaResponse {
  rpc.Status status = 1;
  string kfs_doc_url = 2;
  string loan_agreement_doc_url = 3;
}

message SignKfsLaRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2 [(validate.rules).string.min_len = 1];
  // User id of the user who is signing the KFS LA
  string user_id = 3 [(validate.rules).string.min_len = 1];
  // List of consent codes that the user has consented to
  repeated ConsentType consent_code_list = 4;
  // denotes the user's device ip from which they are applying for the loan.
  string user_ip = 5 [(validate.rules).string.ip = true];
  // denotes the user's device id from which they are applying for the loan.
  string device_id = 6 [(validate.rules).string.min_len = 1];
  // [Optional] latitude and longitude of the user from where they are applying for the loan.
  string latitude = 7;
  string longitude = 8;
  // denotes the time when the user has given consent
  google.protobuf.Timestamp consent_time = 9 [(validate.rules).timestamp.required = true];
}

message SignKfsLaResponse {
  rpc.Status status = 1;
  string kfs_doc_url = 2;
  string loan_agreement_doc_url = 3;
  google.protobuf.Timestamp modify_roi_expiration_time = 4;
}

message GetLoanDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2;
}

message GetLoanDetailsResponse {
  rpc.Status status = 1;
  LoanStatus loan_status = 2;
  vendors.lenden.LoanDetails loan_details = 3;
}

message GetPreDisbursementDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  google.type.Money amount = 2 [(validate.rules).message = {required: true}];
  int32 tenure = 3 [(validate.rules).int32.gt = 0];
  google.type.Decimal rate_of_interest = 4 [(validate.rules).message = {required: true}];
}

message GetPreDisbursementDetailsResponse {
  enum Status {
    OK = 0;
    // When values of one or more field in input is out of range
    OUT_OF_RANGE = 101;
  }
  rpc.Status status = 1;

  PreDisbursementDetails pre_disbursement_details = 2;
}

message PreDisbursementDetails {
  // The fee charged for processing the loan.
  google.type.Money processing_fee = 1;

  // The amount of money that will be disbursed to the borrower.
  google.type.Money disbursal_amount = 2;

  // The total interest that will be paid over the life of the loan.
  google.type.Money total_interest = 3;

  // The total amount that will be repaid by the borrower, including principal and interest.
  google.type.Money total_repayment_amount = 4;

  // The amount of each installment payment.
  google.type.Money installment_amount = 5;

  // The date of the first installment payment.
  google.type.Date first_installment_date = 6;

  // The annual percentage rate (APR) of the loan.
  double apr = 7;

  // The interest charged for the gap period between disbursement and the first installment.
  google.type.Money gap_interest = 8;
}

message PostExternalDataRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2;
  string user_id = 3;
  bytes data = 4;
  BankDetails bank_details = 5;
  EligibilityDataType type = 6;
}

message BankDetails {
  string holder_name = 1;
  string account_number = 2;
  string ifsc = 3;
  AccountType type = 4;
}

message PostExternalDataResponse {
  rpc.Status status = 1;
}

message GetAmortizationScheduleRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2;
}

enum AmortizationComponentPurpose {
  AMORTIZATION_COMPONENT_PURPOSE_UNSPECIFIED = 0;

  AMORTIZATION_COMPONENT_PURPOSE_PRINCIPAL = 1;

  AMORTIZATION_COMPONENT_PURPOSE_INTEREST = 2;
}

message AmortizationAmountBreakupComponent {
  AmortizationComponentPurpose purpose = 1;
  google.type.Money amount = 2;
}

enum AmortizationScheduleItemStatus {
  AMORTIZATION_SCHEDULE_ITEM_STATUS_UNSPECIFIED = 0;

  AMORTIZATION_SCHEDULE_ITEM_STATUS_UPCOMING = 1;

  AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID = 2;

  AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID_IN_ADVANCE = 3;

  AMORTIZATION_SCHEDULE_ITEM_STATUS_DUE = 4;

  AMORTIZATION_SCHEDULE_ITEM_STATUS_OVERDUE = 5;

  AMORTIZATION_SCHEDULE_ITEM_STATUS_LATE_PAYMENT = 6;

  AMORTIZATION_SCHEDULE_ITEM_STATUS_PARTIALLY_PAID = 7;
}

message AmortizationScheduleItem {
  google.type.Date due_date = 1;
  google.type.Money due_amount = 2;
  repeated AmortizationAmountBreakupComponent breakup = 3;
  AmortizationScheduleItemStatus status = 4;
  google.type.Money outstanding_amount = 5;
}

message GetAmortizationScheduleResponse {
  rpc.Status status = 1;
  repeated AmortizationScheduleItem amortization_schedule = 2;
}


message GetForeclosureDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2;

  enum Purpose {
    PURPOSE_UNSPECIFIED = 0;

    PURPOSE_FORECLOSURE = 1;

    // For getting foreclosure details of a loan in the cool-off / cancellation period
    PURPOSE_COOL_OFF = 2;
  }
  Purpose purpose = 3;
}

message GetForeclosureDetailsResponse {
  enum Status {
    OK = 0;

    // Can be sent when a loan is already closed and thus foreclosure isn't applicable
    INVALID_LOAN_STATUS = 101;

    // If a user pays more than their EMI, LDC allocates the excess amount to principal first,
    // and then to interest. As a result, a user may fully repay the principal while still
    // having pending interest. In this case, foreclosure charges do not apply and the loan
    // can be closed by simply paying the remaining interest amount.
    // LDC refers to this as a "normal closure" instead of a foreclosure.
    // The remaining amount for closing the loan can be retrieved using the GetLoanDetails API.
    PRINCIPAL_PAID = 102;

    // A user can't foreclose a loan if their last EMI due date is less than 3 days away.
    // They can close the loan via normal route in such a situation.
    LAST_DUE_DATE_CLOSE = 103;
  }
  rpc.Status status = 1;

  ForeclosureDetails foreclosure_details = 2;
}

message ForeclosureDetails {
  google.type.Money principal_outstanding = 1;
  google.type.Money interest_outstanding = 2;
  google.type.Money delay_charges_outstanding = 3;
  google.type.Money late_fee_outstanding = 4;
  google.type.Money foreclosure_amount = 5;
  google.type.Money foreclosure_charges = 6;
  google.type.Money repayment_received = 7;
}

message GeneratePaymentLinkRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2 [(validate.rules).string.min_len = 1];
  google.type.Money amount = 3 [(validate.rules).message = {required: true}];
}

message GeneratePaymentLinkResponse {
  rpc.Status status = 1;
  string url = 2;
  string order_id = 3;
}

message GetPaymentStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string order_id = 2 [(validate.rules).string.min_len = 1];
}

enum PaymentStatus {
  PAYMENT_STATUS_UNSPECIFIED = 0;

  PAYMENT_STATUS_PENDING = 1;

  PAYMENT_STATUS_SUCCESS = 2;

  PAYMENT_STATUS_FAILED = 3;

  // This can happen in case like when the user does not act on the payment link
  // and it expires after a certain time like 15 mins
  PAYMENT_STATUS_CANCELLED = 4;
}

message GetPaymentStatusResponse {
  rpc.Status status = 1;

  PaymentStatus payment_status = 2;

  string failure_reason = 3;
}
