### CKYC Verification Flow (Federal CKYC)

This document explains how to use `api/vendorgateway/ckyc/service.proto` to perform CKYC verification via the Federal-based CKYC APIs.

### Overview

- **Search**: Check if a CKYC record exists using PAN and mobile number. If found, you receive a `ckyc_reference_id`.
- **GetData**: Start OTP flow using the `ckyc_reference_id` and mobile number. You receive an `otp_request_id`.
- **VerifyOTP**: Validate OTP (or resend) using `otp_request_id` and `otp`. On successful validation, CKYC data is returned.

### Mermaid Flow Diagram

```mermaid
sequenceDiagram
  autonumber
  participant Client
  participant CKyc as CKyc Service
  participant Federal as Federal CKYC

  Note over Client,CKyc: 1) Search for CKYC record by PAN + Mobile
  Client->>CKyc: Search(SearchRequest: kyc_document[PAN], mobile_number)
  CKyc->>Federal: Search
  Federal-->>CKyc: SearchResponse(records, ckyc_reference_id)
  CKyc-->>Client: SearchResponse(records, ckyc_reference_id)

  alt Record found (ckyc_reference_id present)
    Note over Client,CKyc: 2) Initiate OTP for data retrieval
    Client->>CKyc: GetData(GetDataRequest: ckyc_reference_id, mobile_number, auth_factor_type=MOBILE_NUMBER)
    CKyc->>Federal: GetData (triggers OTP to registered mobile)
    Federal-->>CKyc: GetDataResponse(otp_request_id)
    CKyc-->>Client: GetDataResponse(otp_request_id)

    alt Submit OTP to retrieve CKYC data
      Note over Client,CKyc: 3) Verify OTP (or resend if OTP omitted)
      Client->>CKyc: VerifyOTP(VerifyOTPRequest: otp_request_id, otp)
      CKyc->>Federal: VerifyOTP
      Federal-->>CKyc: VerifyOTPResponse(payload | error status)
      CKyc-->>Client: VerifyOTPResponse(payload | error status)
    else Resend OTP
      Client->>CKyc: VerifyOTP(VerifyOTPRequest: otp_request_id, otp="")
      CKyc->>Federal: Resend OTP
      Federal-->>CKyc: VerifyOTPResponse(status=OTP_RESENT_SUCCESSFULLY)
      CKyc-->>Client: VerifyOTPResponse(status=OTP_RESENT_SUCCESSFULLY)
    end
  else No record found
    CKyc-->>Client: SearchResponse(records=[], ckyc_reference_id="")
  end
```

### Block Diagram

<svg width="980" height="520" viewBox="0 0 980 520" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="CKYC block diagram">
  <defs>
    <marker id="arrow" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
      <path d="M 0 0 L 10 5 L 0 10 z" fill="#333"/>
    </marker>
    <style>
      .box{fill:#fff;stroke:#333;stroke-width:2;rx:8;ry:8}
      .title{font: 700 16px system-ui, -apple-system, Segoe UI, Roboto, sans-serif; fill:#111}
      .label{font: 500 14px system-ui, -apple-system, Segoe UI, Roboto, sans-serif; fill:#333}
      .small{font: 12px system-ui, -apple-system, Segoe UI, Roboto, sans-serif; fill:#555}
      .annot{font: 12px system-ui, -apple-system, Segoe UI, Roboto, sans-serif; fill:#111}
      .pill{fill:#f5f5f5;stroke:#bbb;stroke-width:1;rx:6;ry:6}
    </style>
  </defs>

  <!-- Columns -->
  <rect x="40" y="40" width="240" height="440" class="box"/>
  <text x="60" y="68" class="title">Client</text>

  <rect x="370" y="40" width="240" height="440" class="box"/>
  <text x="390" y="68" class="title">CKyc Service</text>

  <rect x="700" y="40" width="240" height="440" class="box"/>
  <text x="720" y="68" class="title">Federal CKYC</text>

  <!-- Client actions -->
  <rect x="60" y="100" width="200" height="60" class="pill"/>
  <text x="70" y="126" class="label">SearchRequest</text>
  <text x="70" y="146" class="small">PAN + Mobile</text>

  <rect x="60" y="200" width="200" height="60" class="pill"/>
  <text x="70" y="226" class="label">GetDataRequest</text>
  <text x="70" y="246" class="small">ckyc_reference_id + Mobile</text>

  <rect x="60" y="300" width="200" height="60" class="pill"/>
  <text x="70" y="326" class="label">VerifyOTPRequest</text>
  <text x="70" y="346" class="small">otp_request_id + OTP or empty</text>

  <!-- Service ops -->
  <rect x="390" y="110" width="200" height="40" class="pill"/>
  <text x="400" y="136" class="label">Search</text>

  <rect x="390" y="210" width="200" height="40" class="pill"/>
  <text x="400" y="236" class="label">GetData (trigger OTP)</text>

  <rect x="390" y="310" width="200" height="40" class="pill"/>
  <text x="400" y="336" class="label">VerifyOTP (validate / resend)</text>

  <!-- Federal ops -->
  <rect x="720" y="110" width="200" height="40" class="pill"/>
  <text x="730" y="136" class="label">Search</text>

  <rect x="720" y="210" width="200" height="40" class="pill"/>
  <text x="730" y="236" class="label">GetData (send OTP)</text>

  <rect x="720" y="310" width="200" height="40" class="pill"/>
  <text x="730" y="336" class="label">VerifyOTP (payload/status)</text>

  <!-- Flows -->
  <!-- Search -->
  <line x1="260" y1="130" x2="390" y2="130" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="280" y="120" class="annot">Search</text>
  <line x1="590" y1="130" x2="720" y2="130" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="920" y1="150" x2="610" y2="150" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="700" y="168" class="annot">records + ckyc_reference_id</text>
  <line x1="410" y1="150" x2="260" y2="150" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="120" y="168" class="annot">records + ckyc_reference_id</text>

  <!-- GetData / OTP -->
  <line x1="260" y1="230" x2="390" y2="230" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="280" y="220" class="annot">GetData</text>
  <line x1="590" y1="230" x2="720" y2="230" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="625" y="220" class="small">triggers OTP</text>
  <line x1="920" y1="250" x2="610" y2="250" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="700" y="268" class="annot">otp_request_id</text>
  <line x1="410" y1="250" x2="260" y2="250" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="120" y="268" class="annot">otp_request_id</text>

  <!-- Verify OTP / Resend -->
  <line x1="260" y1="330" x2="390" y2="330" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="280" y="320" class="annot">VerifyOTP</text>
  <line x1="590" y1="330" x2="720" y2="330" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="920" y1="350" x2="610" y2="350" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="700" y="368" class="annot">payload or error status</text>
  <line x1="410" y1="350" x2="260" y2="350" stroke="#333" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="110" y="368" class="annot">payload/status to client</text>

  <!-- Resend hint -->
  <text x="60" y="400" class="small">Resend: send VerifyOTPRequest with empty otp</text>
</svg>

### Messages and Fields

- **SearchRequest**
  - `kyc_document` (`kyc.IdProof`): Must include PAN.
  - `mobile_number` (`api.typesv2.common.PhoneNumber`).

- **SearchResponse**
  - `ckyc_reference_id` (string): Use in `GetDataRequest` when present.
  - `records` (partial `Record` items): Basic profile preview.

- **GetDataRequest**
  - `ckyc_reference_id` (string): From `SearchResponse`.
  - `mobile_number` (`api.typesv2.common.PhoneNumber`).
  - `auth_factor_type = MOBILE_NUMBER` (required in V3 flow).

- **GetDataResponse**
  - `otp_request_id` (string): Use in `VerifyOTPRequest`.

- **VerifyOTPRequest**
  - `otp_request_id` (string): From `GetDataResponse`.
  - `otp` (string): Provide to validate; leave empty to resend.

- **VerifyOTPResponse**
  - `payload` (`kyc.CKYCDownloadPayload`): CKYC data on successful OTP validation.
  - `status` includes: `INVALID_OTP`, `OTP_EXPIRED`, `VERIFICATION_TEMP_BLOCKED`, `RESEND_OTP_LIMIT_EXCEEDED`, `RESEND_OTP_COOLDOWN_ACTIVE`, `OTP_VALIDATION_FAILED_AND_RETRY_EXHAUSTED`, `OTP_RESENT_SUCCESSFULLY`.

### Typical Happy Path

1. Call `CKyc.Search` with PAN and mobile → get `ckyc_reference_id`.
2. Call `CKyc.GetData` with `ckyc_reference_id` and mobile → receive `otp_request_id` (OTP sent to registered number).
3. Call `CKyc.VerifyOTP` with `otp_request_id` and `otp` → receive CKYC `payload`.

### Resend OTP

To resend the OTP, call `CKyc.VerifyOTP` with an empty `otp`.

Example request (abbreviated):

```json
{
  "header": { "request_id": "<uuid>" },
  "otp_request_id": "<from GetDataResponse>",
  "otp": ""
}
```

### Notes

- `ckyc_number` in `GetDataRequest` is deprecated; always use `ckyc_reference_id`.
- Only `MOBILE_NUMBER` auth factor is supported in current (V3) integration.
- To resend OTP, call `VerifyOTP` with an empty `otp`.
// To be in sync with api/vendorgateway/ckyc/service.proto
