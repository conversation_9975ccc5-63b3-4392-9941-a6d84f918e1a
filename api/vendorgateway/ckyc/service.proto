// protolint:disable MAX_LINE_LENGTH

// Defines a service for integrating with the "Central KYC Registry" which is
// a centralized repository of KYC records of customers in the financial sector.
// APIs here are based on the patchy documents. Refinement will be done later.
// Please refer https://testbed.ckycindia.in/ckyc/?r=download

syntax = "proto3";

package vendorgateway.ckyc;

import "api/kyc/kyc.proto";
import "api/kyc/ckyc_download.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/request_header.proto";
import "api/rpc/status.proto";
import "api/vendorgateway/vendor_status.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/ckyc";
option java_package = "com.github.epifi.gamma.api.vendorgateway.ckyc";

message SearchRequest {
  vendorgateway.RequestHeader header = 1;
  kyc.IdProof kyc_document = 2;
  api.typesv2.common.PhoneNumber mobile_number = 3;
}

message SearchResponse {
  rpc.Status status = 2;
  vendorgateway.VendorStatus vendor_status = 3;

  // In the search response these records are partially filled.
  repeated Record records = 1;
  // reference id for the ckyc record using which download request will be made
  string ckyc_reference_id = 4;
}

message GetDataRequest {
  vendorgateway.RequestHeader header = 1;
  // CKYC record number for which data is to be retrieved.
  // Deprecated: use ckyc reference id instead
  string ckyc_number = 2 [deprecated = true];
  AuthFactorType auth_factor_type = 3;
  // For DATE_OF_BIRTH_OR_INCORPORATION the date in DD-MM-YYYY format.
  // For PINCODE_AND_YEAR_OF_BIRTH 6 digit pin code + 4 digit year.
  // For MOBILE_NUMBER 10 digit phone number.
  string auth_factor_value = 4;
  api.typesv2.common.PhoneNumber mobile_number = 5;
  // ckyc reference id received in the CKYC search response
  string ckyc_reference_id = 6;
}

message GetDataResponse {
  enum Status {
    OK = 0;
    DOB_MISMATCH = 101;
    PHONE_NUMBER_MISMATCH = 102;
    PHONE_NUMBER_NOT_LINKED = 103;
  }
  rpc.Status status = 3;
  vendorgateway.VendorStatus vendor_status = 4;

  kyc.CKYCDownloadPayload payload = 1 [deprecated = true];
  string request_id = 2;
  string otp_request_id = 5;
}

message Record {
  string ckyc_number = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.common.Name fathers_name = 3;
  api.typesv2.common.Image photo = 4;

  // date when kyc was first recorded
  google.type.Date kyc_date = 6;
  // date when kyc record was last updated
  google.type.Date updated_date = 7;
  kyc.IdProof kyc_document = 8;
  int32 age = 9;
}

enum AuthFactorType {
  AUTH_FACTOR_TYPE_UNSPECIFIED = 0;
  // deprrecated dob and pincode auth factor as in V3 api only mobile number is required and it is mandatory
  DATE_OF_BIRTH_OR_INCORPORATION = 1 [deprecated = true];
  PINCODE_AND_YEAR_OF_BIRTH = 2 [deprecated = true];
  MOBILE_NUMBER = 3;
}

message VerifyOTPRequest {
  vendorgateway.RequestHeader header = 1;
  string request_id = 2;
  // otp request id received in GetDataResponse
  string otp_request_id = 3;
  // if otp is present in the request then it will be verified.
  // if otp is empty in the request then it will resend otp
  string otp = 4;
}

message VerifyOTPResponse {
  enum Status {
    OK = 0;
    // User entered a wrong OTP
    INVALID_OTP = 101;
    // User submitted OTP after it's expiry
    OTP_EXPIRED = 102;
    // User temporarily blocked to prevent excessive retries
    VERIFICATION_TEMP_BLOCKED = 103;
    // Resend OTP limit exceeded
    RESEND_OTP_LIMIT_EXCEEDED = 104;
    // This is expected to be returned if OTP is requested to be resent within 90 seconds of the previous request.
    RESEND_OTP_COOLDOWN_ACTIVE = 105;
    // This is returned when a user has exhausted all retries for OTP validation
    OTP_VALIDATION_FAILED_AND_RETRY_EXHAUSTED = 106;
    OTP_RESENT_SUCCESSFULLY = 107;
  }
  rpc.Status status = 1;
  vendorgateway.VendorStatus vendor_status = 2;
  kyc.CKYCDownloadPayload payload = 3;
  string request_id = 4;
}

service CKyc {
  // Searches the ckyc database and returns ckyc records that match the search
  // criteria. Search criteria include different kyc document identifiers along
  // data like date of birth gender etc.
  rpc Search (SearchRequest) returns (SearchResponse);

  // GetData is used to initiate the otp verification procedure for first time for a customer
  rpc GetData (GetDataRequest) returns (GetDataResponse);

  // VerifyOTP is used to verify the otp, resend the otp, if the otp is verified then it returns the data of coustomer as well
  // if otp is present in the request then it will be verified.
  // if otp is empty in the request then it will resend otp
  // https://docs.google.com/document/d/1gn-Jq9O71qrytXjRCnBmh3sbLkHY4Lx0/edit
  rpc VerifyOTP (VerifyOTPRequest) returns (VerifyOTPResponse);
}
