// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/aml/common.proto

package aml

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CaseCategory int32

const (
	CaseCategory_CASE_CATEGORY_UNSPECIFIED CaseCategory = 0
	CaseCategory_CASE_CATEGORY_OPEN        CaseCategory = 1
	CaseCategory_CASE_CATEGORY_PENDING     CaseCategory = 2
	CaseCategory_CASE_CATEGORY_COMPLETED   CaseCategory = 3
)

// Enum value maps for CaseCategory.
var (
	CaseCategory_name = map[int32]string{
		0: "CASE_CATEGORY_UNSPECIFIED",
		1: "CASE_CATEGORY_OPEN",
		2: "CASE_CATEGORY_PENDING",
		3: "CASE_CATEGORY_COMPLETED",
	}
	CaseCategory_value = map[string]int32{
		"CASE_CATEGORY_UNSPECIFIED": 0,
		"CASE_CATEGORY_OPEN":        1,
		"CASE_CATEGORY_PENDING":     2,
		"CASE_CATEGORY_COMPLETED":   3,
	}
)

func (x CaseCategory) Enum() *CaseCategory {
	p := new(CaseCategory)
	*p = x
	return p
}

func (x CaseCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_common_proto_enumTypes[0].Descriptor()
}

func (CaseCategory) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_common_proto_enumTypes[0]
}

func (x CaseCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseCategory.Descriptor instead.
func (CaseCategory) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_common_proto_rawDescGZIP(), []int{0}
}

type CaseType int32

const (
	CaseType_CASE_TYPE_UNSPECIFIED     CaseType = 0
	CaseType_CASE_TYPE_INITIAL         CaseType = 1
	CaseType_CASE_TYPE_WATCHLIST_ADDED CaseType = 2
)

// Enum value maps for CaseType.
var (
	CaseType_name = map[int32]string{
		0: "CASE_TYPE_UNSPECIFIED",
		1: "CASE_TYPE_INITIAL",
		2: "CASE_TYPE_WATCHLIST_ADDED",
	}
	CaseType_value = map[string]int32{
		"CASE_TYPE_UNSPECIFIED":     0,
		"CASE_TYPE_INITIAL":         1,
		"CASE_TYPE_WATCHLIST_ADDED": 2,
	}
)

func (x CaseType) Enum() *CaseType {
	p := new(CaseType)
	*p = x
	return p
}

func (x CaseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_common_proto_enumTypes[1].Descriptor()
}

func (CaseType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_common_proto_enumTypes[1]
}

func (x CaseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseType.Descriptor instead.
func (CaseType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_common_proto_rawDescGZIP(), []int{1}
}

type MatchType int32

const (
	MatchType_MATCH_TYPE_UNSPECIFIED MatchType = 0
	// Confirmed hit/match against some watchlist
	MatchType_MATCH_TYPE_CONFIRMED MatchType = 1
	// Probable hit/match
	MatchType_MATCH_TYPE_PROBABLE MatchType = 2
)

// Enum value maps for MatchType.
var (
	MatchType_name = map[int32]string{
		0: "MATCH_TYPE_UNSPECIFIED",
		1: "MATCH_TYPE_CONFIRMED",
		2: "MATCH_TYPE_PROBABLE",
	}
	MatchType_value = map[string]int32{
		"MATCH_TYPE_UNSPECIFIED": 0,
		"MATCH_TYPE_CONFIRMED":   1,
		"MATCH_TYPE_PROBABLE":    2,
	}
)

func (x MatchType) Enum() *MatchType {
	p := new(MatchType)
	*p = x
	return p
}

func (x MatchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_common_proto_enumTypes[2].Descriptor()
}

func (MatchType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_common_proto_enumTypes[2]
}

func (x MatchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchType.Descriptor instead.
func (MatchType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_common_proto_rawDescGZIP(), []int{2}
}

var File_api_vendorgateway_aml_common_proto protoreflect.FileDescriptor

var file_api_vendorgateway_aml_common_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2a, 0x7d, 0x0a, 0x0c, 0x43, 0x61, 0x73, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x53, 0x45, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x19,
	0x0a, 0x15, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41, 0x53,
	0x45, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x5b, 0x0a, 0x08, 0x43, 0x61, 0x73, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a,
	0x11, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x4c, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x57, 0x41, 0x54, 0x43, 0x48, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x45,
	0x44, 0x10, 0x02, 0x2a, 0x5a, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49,
	0x52, 0x4d, 0x45, 0x44, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x42, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x42,
	0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x5a,
	0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x6d, 0x6c, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_aml_common_proto_rawDescOnce sync.Once
	file_api_vendorgateway_aml_common_proto_rawDescData = file_api_vendorgateway_aml_common_proto_rawDesc
)

func file_api_vendorgateway_aml_common_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_aml_common_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_aml_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_aml_common_proto_rawDescData)
	})
	return file_api_vendorgateway_aml_common_proto_rawDescData
}

var file_api_vendorgateway_aml_common_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_vendorgateway_aml_common_proto_goTypes = []interface{}{
	(CaseCategory)(0), // 0: vendorgateway.aml.CaseCategory
	(CaseType)(0),     // 1: vendorgateway.aml.CaseType
	(MatchType)(0),    // 2: vendorgateway.aml.MatchType
}
var file_api_vendorgateway_aml_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_aml_common_proto_init() }
func file_api_vendorgateway_aml_common_proto_init() {
	if File_api_vendorgateway_aml_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_aml_common_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_aml_common_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_aml_common_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_aml_common_proto_enumTypes,
	}.Build()
	File_api_vendorgateway_aml_common_proto = out.File
	file_api_vendorgateway_aml_common_proto_rawDesc = nil
	file_api_vendorgateway_aml_common_proto_goTypes = nil
	file_api_vendorgateway_aml_common_proto_depIdxs = nil
}
