syntax = "proto3";

package vendorgateway.aml;

import "api/aml/data.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/document_proof.proto";
import "api/typesv2/common/employment_type.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/income_slab.proto";
import "api/typesv2/common/marital_status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/nationality.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/politically_exposed_status.proto";
import "api/vendorgateway/aml/case.proto";
import "api/vendorgateway/aml/common.proto";
import "api/vendorgateway/request_header.proto";
import "google/type/date.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/aml";
option java_package = "com.github.epifi.gamma.api.vendorgateway.aml";

// Aml service is used to communicate with AML vendors for screening our customers
service Aml {
  // RPC to check the user's details for any money laundering activities
  // by querying against the vendor's database of money launderers.
  rpc ScreenCustomer (ScreenCustomerRequest) returns (ScreenCustomerResponse);

  // Initiate screening a user's details against money-laundering, terrorist financing, and/or adverse-media activities
  // by matching against configured watchlists
  rpc InitiateScreening(InitiateScreeningRequest) returns (InitiateScreeningResponse);

  // List cases with information for cases that are created or modified in the given date range
  rpc ListCases(ListCasesRequest) returns (ListCasesResponse);

  // Get details of a specific case ID
  rpc GetCaseDetails(GetCaseDetailsRequest) returns (GetCaseDetailsResponse);
}

message ScreenCustomerRequest {
  vendorgateway.RequestHeader header = 1;
  // unique for each actor
  string record_identifier = 2;
  // request id to be passed to vendor
  string vendor_request_id = 3;
  // deprecated since AML screening rules are configured based on AmlProduct and this is of no use
  .aml.AmlEntity entity = 4 [deprecated = true];
  .aml.AmlProduct product = 5;
  CustomerDetails customer_details = 6 [(validate.rules).message.required = true];

  // Owner of the screening request.
  // Determines the correct API URL and configuration needed for the screening request with the vendor.
  // The API URL and configuration may differ by owner to comply with regulations,
  // ensuring only the owner’s designated case reviewer can access and review potential matches on money launderer lists.
  api.typesv2.common.Owner owner = 7;
}

message ScreenCustomerResponse {
  rpc.Status status = 1;
  // rejection message if the request is rejected
  string rejection_message = 2;
  // rejection code if the request is rejected
  .aml.RejectionCode rejection_code = 3;
  // record identifier passed in the request
  string record_identifier = 4;
  MatchStatus match_status = 5;
  // case id if matched and case is generated
  string case_id = 6;
  // case link if matched and case is generated
  string case_link = 7;
  // count of matches found
  uint64 alert_count = 8;
  // details of all the matches
  repeated .aml.MatchDetails match_details = 9;
}

message CustomerDetails {
  // name of the customer - Mandatory
  api.typesv2.common.Name name = 1 [(validate.rules).message.required = true];
  // father name - NOT Mandatory
  api.typesv2.common.Name father_name = 2;
  // mother name - NOT Mandatory
  api.typesv2.common.Name mother_name = 3;
  // gender - NOT Mandatory
  api.typesv2.common.Gender gender = 4;
  // marital status - NOT Mandatory
  api.typesv2.common.MaritalStatus marital_status = 5;
  // income slab - NOT Mandatory
  api.typesv2.common.IncomeSlab income_slab = 6;
  // pan number - NOT Mandatory
  string pan_number = 7;
  // nationality - Mandatory
  api.typesv2.common.Nationality nationality = 8 [(validate.rules).enum = {not_in: [0]}];
  // passport id number - NOT Mandatory
  string passport_number = 9;
  // passport expiry date - Mandatory if passport provided
  google.type.Date passport_expiry_date = 10;
  // driving license id number - NOT Mandatory
  string driving_license_number = 11;
  // passport expiry date - Mandatory if driving license provided
  google.type.Date driving_license_expiry_date = 12;
  // voter id number - NOT Mandatory
  string voter_id = 13;
  // document type of proof of address provided - NOT Mandatory
  api.typesv2.common.DocumentProofType poa_type = 14;
  // phone number - NOT Mandatory
  api.typesv2.common.PhoneNumber phone_number = 15;
  // email - NOT Mandatory
  string email = 16;
  // date of birth - NOT Mandatory
  google.type.Date date_of_birth = 17;
  // permanent address - NOT Mandatory
  api.typesv2.common.PostalAddress permanent_address = 18;
  // correspondence address - NOT Mandatory
  api.typesv2.common.PostalAddress correspondence_address = 19;
  // politically exposed status - NOT Mandatory
  api.typesv2.common.PoliticallyExposedStatus politically_exposed_status = 20;
  // employment type - NOT Mandatory
  api.typesv2.common.EmploymentType employment_type = 21;
}

// represents whether a match is found or not after screening is done
enum MatchStatus {
  MATCH_STATUS_UNSPECIFIED = 0;
  // Match found in screening
  MATCH_STATUS_MATCHED = 1;
  // Match not found in screening
  MATCH_STATUS_NOT_MATCHED = 2;
  // Error from vendor in screening attempt
  MATCH_STATUS_ERROR = 3;
}

message InitiateScreeningRequest {
  vendorgateway.RequestHeader header = 1;
  api.typesv2.common.Owner owner = 2;
  // Unique identifier of a user in caller's system to refer later during case reviews
  string user_id = 3;
  // Unique request id to be passed to vendor
  string vendor_request_id = 4;
  Product product = 5;
  Purpose purpose = 6;
  CustomerDetails user_details = 7 [(validate.rules).message.required = true];
}

// Product is a set of pre-configured watchlists and screening parameters used for screening a user's details against.
// This is useful for clients to use for common financial products instead of sending individual watchlists and parameters.
enum Product {
  PRODUCT_UNSPECIFIED = 0;
  PRODUCT_MUTUAL_FUND = 1;
  PRODUCT_LOAN = 2;
  PRODUCT_US_STOCKS = 3;
  PRODUCT_BANK_ACCOUNT = 4;
}

// Purpose is used to decide which type of screening to perform
enum Purpose {
  PURPOSE_UNSPECIFIED = 0;
  // To screen a user with the given details once
  PURPOSE_INITIAL_SCREENING = 1;
  // To keep screening the user not just now, but also in future if his details match
  // any new entries in the watchlists as the watchlists keep getting updated periodically
  PURPOSE_CONTINUOUS_SCREENING = 2;
}

message InitiateScreeningResponse {
  rpc.Status status = 1;
  // Request ID from the original request
  string request_id = 2;
  // Overall status of the request
  OverallStatus overall_status = 3;
  // Main validation code (if any)
  string validation_code = 4;
  // Main validation description (if any)
  string validation_description = 5;
  UserScreeningResult user_screening_result = 6;
}

enum OverallStatus {
  OVERALL_STATUS_UNSPECIFIED = 0;
  // The screening request was accepted and the AML check process was started.
  // In case no hit is found for the user details against any watchlist, then the process ends here.
  // If hits are found, then a case will be created for this request for manual review.
  // Some details of hits are also provided in the response in case of hits.
  OVERALL_STATUS_ACCEPTED = 1;
  // The screening request was rejected, most likely due to some error validating request parameters.
  OVERALL_STATUS_REJECTED = 2;
}

message UserScreeningResult {
  // Unique identifier of a user in caller's system to refer later during case reviews
  string user_id = 1;
  // Outcome of validation of user's details
  ValidationOutcome validation_outcome = 2;
  // Overall suggested action for this user
  SuggestedAction suggested_action = 3;
  // Response for a purpose requested
  PurposeScreeningResult purpose_screening_result = 4;
  // Validation code for any validation failures in user's details
  string validation_code = 5;
  // Validation description for any validation failures in user's details
  string validation_description = 6;
  // Number of validation failures for this user
  int32 validation_failure_count = 7;
}

enum ValidationOutcome {
  VALIDATION_OUTCOME_UNSPECIFIED = 0;
  VALIDATION_OUTCOME_SUCCESS = 1;
  VALIDATION_OUTCOME_FAILURE = 2;
}

// Suggested action based on screening results
enum SuggestedAction {
  SUGGESTED_ACTION_UNSPECIFIED = 0;
  // Proceed with the user's onboarding (no hits found)
  SUGGESTED_ACTION_PROCEED = 1;
  // Review the user (probable hits found)
  SUGGESTED_ACTION_REVIEW = 2;
  // Stop the user onboarding (confirmed hits found)
  SUGGESTED_ACTION_STOP = 3;
}

message PurposeScreeningResult {
  // Name/description of the purpose
  string purpose = 1;
  // Purpose code (01, 02, 03, 04, 05)
  string purpose_code = 2;
  // Validation code for this purpose
  string validation_code = 3;
  // Validation description for this purpose
  string validation_description = 4;
  // Number of validation failures for this purpose
  int32 validation_failure_count = 5;
  // Results of screening for this purpose
  ScreeningResult screening_result = 6;
}

// Data specific to screening purpose
message ScreeningResult {
  // Whether hits were detected (Yes/No)
  bool hits_detected = 1;
  // Total number of hits detected
  int32 hits_count = 2;
  // Whether any confirmed hits were found
  bool confirmed_hit = 3;
  // Case ID generated if hits are found
  string case_id = 4;
  // Case URL for reviewing the hits
  string case_url = 5;
  // Base64 encoded report data
  string report_data = 6;
  // List of individual hits/matches
  repeated Hit hits = 7;
}

// Represents a single hit/match found during screening
message Hit {
  // Source of the watchlist where the hit was found
  string source = 1;
  // TrackWizz ID for the watchlist record
  string watchlist_source_id = 2;
  // Type of match (Confirmed, Probable, etc.)
  MatchType match_type = 3;
  // Confidence score assigned to the match
  double score = 4;
  // Attributes that confirmed the match
  string confirmed_matching_attributes = 5;
}

message ListCasesRequest {
  vendorgateway.RequestHeader header = 1 [(validate.rules).message.required = true];
  api.typesv2.common.Owner owner = 2;
  repeated CaseCategory case_categories = 4 [(validate.rules).repeated.min_items = 1];
  google.protobuf.Timestamp from_date_time = 5 [(validate.rules).timestamp.required = true];
  google.protobuf.Timestamp to_date_time = 6 [(validate.rules).timestamp.required = true];
  repeated CaseType case_types = 7 [(validate.rules).repeated.min_items = 1];
}

message ListCasesResponse {
  rpc.Status status = 1;
  string request_id = 2;
  int32 case_count = 3;
  repeated Case cases = 4;
}

message GetCaseDetailsRequest {
  vendorgateway.RequestHeader header = 1 [(validate.rules).message.required = true];
  api.typesv2.common.Owner owner = 2;
  string case_id = 3 [(validate.rules).string.min_len = 1];
}

message GetCaseDetailsResponse {
  rpc.Status status = 1;
  string request_id = 2;
  Case case = 3;
}
