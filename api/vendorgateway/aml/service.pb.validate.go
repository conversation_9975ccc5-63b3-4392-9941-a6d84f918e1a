// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/aml/service.proto

package aml

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	aml "github.com/epifi/gamma/api/aml"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = aml.AmlEntity(0)

	_ = common.Owner(0)
)

// Validate checks the field values on ScreenCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenCustomerRequestMultiError, or nil if none found.
func (m *ScreenCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenCustomerRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenCustomerRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenCustomerRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecordIdentifier

	// no validation rules for VendorRequestId

	// no validation rules for Entity

	// no validation rules for Product

	if m.GetCustomerDetails() == nil {
		err := ScreenCustomerRequestValidationError{
			field:  "CustomerDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCustomerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenCustomerRequestValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenCustomerRequestValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenCustomerRequestValidationError{
				field:  "CustomerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Owner

	if len(errors) > 0 {
		return ScreenCustomerRequestMultiError(errors)
	}

	return nil
}

// ScreenCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by ScreenCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type ScreenCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenCustomerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenCustomerRequestMultiError) AllErrors() []error { return m }

// ScreenCustomerRequestValidationError is the validation error returned by
// ScreenCustomerRequest.Validate if the designated constraints aren't met.
type ScreenCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenCustomerRequestValidationError) ErrorName() string {
	return "ScreenCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenCustomerRequestValidationError{}

// Validate checks the field values on ScreenCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenCustomerResponseMultiError, or nil if none found.
func (m *ScreenCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenCustomerResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RejectionMessage

	// no validation rules for RejectionCode

	// no validation rules for RecordIdentifier

	// no validation rules for MatchStatus

	// no validation rules for CaseId

	// no validation rules for CaseLink

	// no validation rules for AlertCount

	for idx, item := range m.GetMatchDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ScreenCustomerResponseValidationError{
						field:  fmt.Sprintf("MatchDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ScreenCustomerResponseValidationError{
						field:  fmt.Sprintf("MatchDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ScreenCustomerResponseValidationError{
					field:  fmt.Sprintf("MatchDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ScreenCustomerResponseMultiError(errors)
	}

	return nil
}

// ScreenCustomerResponseMultiError is an error wrapping multiple validation
// errors returned by ScreenCustomerResponse.ValidateAll() if the designated
// constraints aren't met.
type ScreenCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenCustomerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenCustomerResponseMultiError) AllErrors() []error { return m }

// ScreenCustomerResponseValidationError is the validation error returned by
// ScreenCustomerResponse.Validate if the designated constraints aren't met.
type ScreenCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenCustomerResponseValidationError) ErrorName() string {
	return "ScreenCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenCustomerResponseValidationError{}

// Validate checks the field values on CustomerDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CustomerDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerDetailsMultiError, or nil if none found.
func (m *CustomerDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() == nil {
		err := CustomerDetailsValidationError{
			field:  "Name",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMotherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMotherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "MotherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	// no validation rules for MaritalStatus

	// no validation rules for IncomeSlab

	// no validation rules for PanNumber

	if _, ok := _CustomerDetails_Nationality_NotInLookup[m.GetNationality()]; ok {
		err := CustomerDetailsValidationError{
			field:  "Nationality",
			reason: "value must not be in list [NATIONALITY_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PassportNumber

	if all {
		switch v := interface{}(m.GetPassportExpiryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PassportExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PassportExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPassportExpiryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "PassportExpiryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DrivingLicenseNumber

	if all {
		switch v := interface{}(m.GetDrivingLicenseExpiryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "DrivingLicenseExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "DrivingLicenseExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDrivingLicenseExpiryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "DrivingLicenseExpiryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VoterId

	// no validation rules for PoaType

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetDateOfBirth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfBirth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "DateOfBirth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCorrespondenceAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "CorrespondenceAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "CorrespondenceAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCorrespondenceAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "CorrespondenceAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PoliticallyExposedStatus

	// no validation rules for EmploymentType

	if len(errors) > 0 {
		return CustomerDetailsMultiError(errors)
	}

	return nil
}

// CustomerDetailsMultiError is an error wrapping multiple validation errors
// returned by CustomerDetails.ValidateAll() if the designated constraints
// aren't met.
type CustomerDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerDetailsMultiError) AllErrors() []error { return m }

// CustomerDetailsValidationError is the validation error returned by
// CustomerDetails.Validate if the designated constraints aren't met.
type CustomerDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerDetailsValidationError) ErrorName() string { return "CustomerDetailsValidationError" }

// Error satisfies the builtin error interface
func (e CustomerDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerDetailsValidationError{}

var _CustomerDetails_Nationality_NotInLookup = map[common.Nationality]struct{}{
	0: {},
}

// Validate checks the field values on InitiateScreeningRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateScreeningRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateScreeningRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateScreeningRequestMultiError, or nil if none found.
func (m *InitiateScreeningRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateScreeningRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateScreeningRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateScreeningRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateScreeningRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Owner

	// no validation rules for UserId

	// no validation rules for VendorRequestId

	// no validation rules for Product

	// no validation rules for Purpose

	if m.GetUserDetails() == nil {
		err := InitiateScreeningRequestValidationError{
			field:  "UserDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetUserDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateScreeningRequestValidationError{
					field:  "UserDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateScreeningRequestValidationError{
					field:  "UserDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateScreeningRequestValidationError{
				field:  "UserDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateScreeningRequestMultiError(errors)
	}

	return nil
}

// InitiateScreeningRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateScreeningRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateScreeningRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateScreeningRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateScreeningRequestMultiError) AllErrors() []error { return m }

// InitiateScreeningRequestValidationError is the validation error returned by
// InitiateScreeningRequest.Validate if the designated constraints aren't met.
type InitiateScreeningRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateScreeningRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateScreeningRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateScreeningRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateScreeningRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateScreeningRequestValidationError) ErrorName() string {
	return "InitiateScreeningRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateScreeningRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateScreeningRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateScreeningRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateScreeningRequestValidationError{}

// Validate checks the field values on InitiateScreeningResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateScreeningResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateScreeningResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateScreeningResponseMultiError, or nil if none found.
func (m *InitiateScreeningResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateScreeningResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateScreeningResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateScreeningResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateScreeningResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for OverallStatus

	// no validation rules for ValidationCode

	// no validation rules for ValidationDescription

	if all {
		switch v := interface{}(m.GetUserScreeningResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateScreeningResponseValidationError{
					field:  "UserScreeningResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateScreeningResponseValidationError{
					field:  "UserScreeningResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserScreeningResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateScreeningResponseValidationError{
				field:  "UserScreeningResult",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateScreeningResponseMultiError(errors)
	}

	return nil
}

// InitiateScreeningResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateScreeningResponse.ValidateAll() if the
// designated constraints aren't met.
type InitiateScreeningResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateScreeningResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateScreeningResponseMultiError) AllErrors() []error { return m }

// InitiateScreeningResponseValidationError is the validation error returned by
// InitiateScreeningResponse.Validate if the designated constraints aren't met.
type InitiateScreeningResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateScreeningResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateScreeningResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateScreeningResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateScreeningResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateScreeningResponseValidationError) ErrorName() string {
	return "InitiateScreeningResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateScreeningResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateScreeningResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateScreeningResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateScreeningResponseValidationError{}

// Validate checks the field values on UserScreeningResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserScreeningResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserScreeningResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserScreeningResultMultiError, or nil if none found.
func (m *UserScreeningResult) ValidateAll() error {
	return m.validate(true)
}

func (m *UserScreeningResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for ValidationOutcome

	// no validation rules for SuggestedAction

	if all {
		switch v := interface{}(m.GetPurposeScreeningResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserScreeningResultValidationError{
					field:  "PurposeScreeningResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserScreeningResultValidationError{
					field:  "PurposeScreeningResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPurposeScreeningResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserScreeningResultValidationError{
				field:  "PurposeScreeningResult",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ValidationCode

	// no validation rules for ValidationDescription

	// no validation rules for ValidationFailureCount

	if len(errors) > 0 {
		return UserScreeningResultMultiError(errors)
	}

	return nil
}

// UserScreeningResultMultiError is an error wrapping multiple validation
// errors returned by UserScreeningResult.ValidateAll() if the designated
// constraints aren't met.
type UserScreeningResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserScreeningResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserScreeningResultMultiError) AllErrors() []error { return m }

// UserScreeningResultValidationError is the validation error returned by
// UserScreeningResult.Validate if the designated constraints aren't met.
type UserScreeningResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserScreeningResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserScreeningResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserScreeningResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserScreeningResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserScreeningResultValidationError) ErrorName() string {
	return "UserScreeningResultValidationError"
}

// Error satisfies the builtin error interface
func (e UserScreeningResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserScreeningResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserScreeningResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserScreeningResultValidationError{}

// Validate checks the field values on PurposeScreeningResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PurposeScreeningResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PurposeScreeningResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PurposeScreeningResultMultiError, or nil if none found.
func (m *PurposeScreeningResult) ValidateAll() error {
	return m.validate(true)
}

func (m *PurposeScreeningResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Purpose

	// no validation rules for PurposeCode

	// no validation rules for ValidationCode

	// no validation rules for ValidationDescription

	// no validation rules for ValidationFailureCount

	if all {
		switch v := interface{}(m.GetScreeningResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PurposeScreeningResultValidationError{
					field:  "ScreeningResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PurposeScreeningResultValidationError{
					field:  "ScreeningResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreeningResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PurposeScreeningResultValidationError{
				field:  "ScreeningResult",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PurposeScreeningResultMultiError(errors)
	}

	return nil
}

// PurposeScreeningResultMultiError is an error wrapping multiple validation
// errors returned by PurposeScreeningResult.ValidateAll() if the designated
// constraints aren't met.
type PurposeScreeningResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PurposeScreeningResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PurposeScreeningResultMultiError) AllErrors() []error { return m }

// PurposeScreeningResultValidationError is the validation error returned by
// PurposeScreeningResult.Validate if the designated constraints aren't met.
type PurposeScreeningResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PurposeScreeningResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PurposeScreeningResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PurposeScreeningResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PurposeScreeningResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PurposeScreeningResultValidationError) ErrorName() string {
	return "PurposeScreeningResultValidationError"
}

// Error satisfies the builtin error interface
func (e PurposeScreeningResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPurposeScreeningResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PurposeScreeningResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PurposeScreeningResultValidationError{}

// Validate checks the field values on ScreeningResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ScreeningResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreeningResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreeningResultMultiError, or nil if none found.
func (m *ScreeningResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreeningResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HitsDetected

	// no validation rules for HitsCount

	// no validation rules for ConfirmedHit

	// no validation rules for CaseId

	// no validation rules for CaseUrl

	// no validation rules for ReportData

	for idx, item := range m.GetHits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ScreeningResultValidationError{
						field:  fmt.Sprintf("Hits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ScreeningResultValidationError{
						field:  fmt.Sprintf("Hits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ScreeningResultValidationError{
					field:  fmt.Sprintf("Hits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ScreeningResultMultiError(errors)
	}

	return nil
}

// ScreeningResultMultiError is an error wrapping multiple validation errors
// returned by ScreeningResult.ValidateAll() if the designated constraints
// aren't met.
type ScreeningResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreeningResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreeningResultMultiError) AllErrors() []error { return m }

// ScreeningResultValidationError is the validation error returned by
// ScreeningResult.Validate if the designated constraints aren't met.
type ScreeningResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreeningResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreeningResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreeningResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreeningResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreeningResultValidationError) ErrorName() string { return "ScreeningResultValidationError" }

// Error satisfies the builtin error interface
func (e ScreeningResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreeningResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreeningResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreeningResultValidationError{}

// Validate checks the field values on Hit with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Hit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Hit with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in HitMultiError, or nil if none found.
func (m *Hit) ValidateAll() error {
	return m.validate(true)
}

func (m *Hit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Source

	// no validation rules for WatchlistSourceId

	// no validation rules for MatchType

	// no validation rules for Score

	// no validation rules for ConfirmedMatchingAttributes

	if len(errors) > 0 {
		return HitMultiError(errors)
	}

	return nil
}

// HitMultiError is an error wrapping multiple validation errors returned by
// Hit.ValidateAll() if the designated constraints aren't met.
type HitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HitMultiError) AllErrors() []error { return m }

// HitValidationError is the validation error returned by Hit.Validate if the
// designated constraints aren't met.
type HitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HitValidationError) ErrorName() string { return "HitValidationError" }

// Error satisfies the builtin error interface
func (e HitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HitValidationError{}

// Validate checks the field values on ListCasesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListCasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCasesRequestMultiError, or nil if none found.
func (m *ListCasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := ListCasesRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCasesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCasesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCasesRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Owner

	if len(m.GetCaseCategories()) < 1 {
		err := ListCasesRequestValidationError{
			field:  "CaseCategories",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFromDateTime() == nil {
		err := ListCasesRequestValidationError{
			field:  "FromDateTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetToDateTime() == nil {
		err := ListCasesRequestValidationError{
			field:  "ToDateTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetCaseTypes()) < 1 {
		err := ListCasesRequestValidationError{
			field:  "CaseTypes",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListCasesRequestMultiError(errors)
	}

	return nil
}

// ListCasesRequestMultiError is an error wrapping multiple validation errors
// returned by ListCasesRequest.ValidateAll() if the designated constraints
// aren't met.
type ListCasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCasesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCasesRequestMultiError) AllErrors() []error { return m }

// ListCasesRequestValidationError is the validation error returned by
// ListCasesRequest.Validate if the designated constraints aren't met.
type ListCasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCasesRequestValidationError) ErrorName() string { return "ListCasesRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListCasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCasesRequestValidationError{}

// Validate checks the field values on ListCasesResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListCasesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCasesResponseMultiError, or nil if none found.
func (m *ListCasesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCasesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCasesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCasesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCasesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for CaseCount

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCasesResponseValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCasesResponseValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCasesResponseValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListCasesResponseMultiError(errors)
	}

	return nil
}

// ListCasesResponseMultiError is an error wrapping multiple validation errors
// returned by ListCasesResponse.ValidateAll() if the designated constraints
// aren't met.
type ListCasesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCasesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCasesResponseMultiError) AllErrors() []error { return m }

// ListCasesResponseValidationError is the validation error returned by
// ListCasesResponse.Validate if the designated constraints aren't met.
type ListCasesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCasesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCasesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCasesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCasesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCasesResponseValidationError) ErrorName() string {
	return "ListCasesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCasesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCasesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCasesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCasesResponseValidationError{}

// Validate checks the field values on GetCaseDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseDetailsRequestMultiError, or nil if none found.
func (m *GetCaseDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetCaseDetailsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Owner

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := GetCaseDetailsRequestValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCaseDetailsRequestMultiError(errors)
	}

	return nil
}

// GetCaseDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetCaseDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCaseDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseDetailsRequestMultiError) AllErrors() []error { return m }

// GetCaseDetailsRequestValidationError is the validation error returned by
// GetCaseDetailsRequest.Validate if the designated constraints aren't met.
type GetCaseDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseDetailsRequestValidationError) ErrorName() string {
	return "GetCaseDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseDetailsRequestValidationError{}

// Validate checks the field values on GetCaseDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCaseDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCaseDetailsResponseMultiError, or nil if none found.
func (m *GetCaseDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCaseDetailsResponseValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCaseDetailsResponseValidationError{
				field:  "Case",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCaseDetailsResponseMultiError(errors)
	}

	return nil
}

// GetCaseDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetCaseDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCaseDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseDetailsResponseMultiError) AllErrors() []error { return m }

// GetCaseDetailsResponseValidationError is the validation error returned by
// GetCaseDetailsResponse.Validate if the designated constraints aren't met.
type GetCaseDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseDetailsResponseValidationError) ErrorName() string {
	return "GetCaseDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCaseDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseDetailsResponseValidationError{}
