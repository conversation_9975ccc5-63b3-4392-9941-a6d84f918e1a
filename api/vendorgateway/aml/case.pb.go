// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/aml/case.proto

package aml

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CaseOf int32

const (
	CaseOf_CASE_OF_UNSPECIFIED        CaseOf = 0
	CaseOf_CASE_OF_MAIN_KYC           CaseOf = 1
	CaseOf_CASE_OF_RELATED_PERSON_KYC CaseOf = 2
)

// Enum value maps for CaseOf.
var (
	CaseOf_name = map[int32]string{
		0: "CASE_OF_UNSPECIFIED",
		1: "CASE_OF_MAIN_KYC",
		2: "CASE_OF_RELATED_PERSON_KYC",
	}
	CaseOf_value = map[string]int32{
		"CASE_OF_UNSPECIFIED":        0,
		"CASE_OF_MAIN_KYC":           1,
		"CASE_OF_RELATED_PERSON_KYC": 2,
	}
)

func (x CaseOf) Enum() *CaseOf {
	p := new(CaseOf)
	*p = x
	return p
}

func (x CaseOf) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseOf) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_case_proto_enumTypes[0].Descriptor()
}

func (CaseOf) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_case_proto_enumTypes[0]
}

func (x CaseOf) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseOf.Descriptor instead.
func (CaseOf) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_case_proto_rawDescGZIP(), []int{0}
}

type InitialScreeningMode int32

const (
	InitialScreeningMode_INITIAL_SCREENING_MODE_UNSPECIFIED    InitialScreeningMode = 0
	InitialScreeningMode_INITIAL_SCREENING_MODE_API            InitialScreeningMode = 1
	InitialScreeningMode_INITIAL_SCREENING_MODE_LOOKUP_IN_BULK InitialScreeningMode = 2
)

// Enum value maps for InitialScreeningMode.
var (
	InitialScreeningMode_name = map[int32]string{
		0: "INITIAL_SCREENING_MODE_UNSPECIFIED",
		1: "INITIAL_SCREENING_MODE_API",
		2: "INITIAL_SCREENING_MODE_LOOKUP_IN_BULK",
	}
	InitialScreeningMode_value = map[string]int32{
		"INITIAL_SCREENING_MODE_UNSPECIFIED":    0,
		"INITIAL_SCREENING_MODE_API":            1,
		"INITIAL_SCREENING_MODE_LOOKUP_IN_BULK": 2,
	}
)

func (x InitialScreeningMode) Enum() *InitialScreeningMode {
	p := new(InitialScreeningMode)
	*p = x
	return p
}

func (x InitialScreeningMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitialScreeningMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_case_proto_enumTypes[1].Descriptor()
}

func (InitialScreeningMode) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_case_proto_enumTypes[1]
}

func (x InitialScreeningMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitialScreeningMode.Descriptor instead.
func (InitialScreeningMode) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_case_proto_rawDescGZIP(), []int{1}
}

type OnboardingDecision int32

const (
	OnboardingDecision_ONBOARDING_DECISION_UNSPECIFIED OnboardingDecision = 0
	OnboardingDecision_ONBOARDING_DECISION_PROCEED     OnboardingDecision = 1
	OnboardingDecision_ONBOARDING_DECISION_DECLINE     OnboardingDecision = 2
)

// Enum value maps for OnboardingDecision.
var (
	OnboardingDecision_name = map[int32]string{
		0: "ONBOARDING_DECISION_UNSPECIFIED",
		1: "ONBOARDING_DECISION_PROCEED",
		2: "ONBOARDING_DECISION_DECLINE",
	}
	OnboardingDecision_value = map[string]int32{
		"ONBOARDING_DECISION_UNSPECIFIED": 0,
		"ONBOARDING_DECISION_PROCEED":     1,
		"ONBOARDING_DECISION_DECLINE":     2,
	}
)

func (x OnboardingDecision) Enum() *OnboardingDecision {
	p := new(OnboardingDecision)
	*p = x
	return p
}

func (x OnboardingDecision) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OnboardingDecision) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_case_proto_enumTypes[2].Descriptor()
}

func (OnboardingDecision) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_case_proto_enumTypes[2]
}

func (x OnboardingDecision) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OnboardingDecision.Descriptor instead.
func (OnboardingDecision) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_case_proto_rawDescGZIP(), []int{2}
}

type AlertDecision int32

const (
	AlertDecision_ALERT_DECISION_UNSPECIFIED AlertDecision = 0
	AlertDecision_ALERT_DECISION_TRUE_MATCH  AlertDecision = 1
	AlertDecision_ALERT_DECISION_NO_MATCH    AlertDecision = 2
	AlertDecision_ALERT_DECISION_PENDING     AlertDecision = 3
)

// Enum value maps for AlertDecision.
var (
	AlertDecision_name = map[int32]string{
		0: "ALERT_DECISION_UNSPECIFIED",
		1: "ALERT_DECISION_TRUE_MATCH",
		2: "ALERT_DECISION_NO_MATCH",
		3: "ALERT_DECISION_PENDING",
	}
	AlertDecision_value = map[string]int32{
		"ALERT_DECISION_UNSPECIFIED": 0,
		"ALERT_DECISION_TRUE_MATCH":  1,
		"ALERT_DECISION_NO_MATCH":    2,
		"ALERT_DECISION_PENDING":     3,
	}
)

func (x AlertDecision) Enum() *AlertDecision {
	p := new(AlertDecision)
	*p = x
	return p
}

func (x AlertDecision) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlertDecision) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_case_proto_enumTypes[3].Descriptor()
}

func (AlertDecision) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_case_proto_enumTypes[3]
}

func (x AlertDecision) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlertDecision.Descriptor instead.
func (AlertDecision) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_case_proto_rawDescGZIP(), []int{3}
}

type Case struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseId                           string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	CaseCreationDateTime             *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=case_creation_date_time,json=caseCreationDateTime,proto3" json:"case_creation_date_time,omitempty"`
	SourceSystemName                 string                 `protobuf:"bytes,3,opt,name=source_system_name,json=sourceSystemName,proto3" json:"source_system_name,omitempty"`
	SourceSystemCustomerCode         string                 `protobuf:"bytes,4,opt,name=source_system_customer_code,json=sourceSystemCustomerCode,proto3" json:"source_system_customer_code,omitempty"`
	ApplicationRefNumber             string                 `protobuf:"bytes,5,opt,name=application_ref_number,json=applicationRefNumber,proto3" json:"application_ref_number,omitempty"`
	CaseOf                           CaseOf                 `protobuf:"varint,6,opt,name=case_of,json=caseOf,proto3,enum=vendorgateway.aml.CaseOf" json:"case_of,omitempty"`
	LinkedToSourceSystemCustomerCode string                 `protobuf:"bytes,7,opt,name=linked_to_source_system_customer_code,json=linkedToSourceSystemCustomerCode,proto3" json:"linked_to_source_system_customer_code,omitempty"`
	Relation                         string                 `protobuf:"bytes,8,opt,name=relation,proto3" json:"relation,omitempty"`
	ScreeningProfile                 string                 `protobuf:"bytes,9,opt,name=screening_profile,json=screeningProfile,proto3" json:"screening_profile,omitempty"`
	ScreeningProfileName             string                 `protobuf:"bytes,10,opt,name=screening_profile_name,json=screeningProfileName,proto3" json:"screening_profile_name,omitempty"`
	CustomerName                     string                 `protobuf:"bytes,11,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	CaseType                         CaseType               `protobuf:"varint,12,opt,name=case_type,json=caseType,proto3,enum=vendorgateway.aml.CaseType" json:"case_type,omitempty"`
	InitialScreeningMode             InitialScreeningMode   `protobuf:"varint,13,opt,name=initial_screening_mode,json=initialScreeningMode,proto3,enum=vendorgateway.aml.InitialScreeningMode" json:"initial_screening_mode,omitempty"`
	OnboardingDecision               OnboardingDecision     `protobuf:"varint,14,opt,name=onboarding_decision,json=onboardingDecision,proto3,enum=vendorgateway.aml.OnboardingDecision" json:"onboarding_decision,omitempty"`
	TotalAlertCount                  int32                  `protobuf:"varint,15,opt,name=total_alert_count,json=totalAlertCount,proto3" json:"total_alert_count,omitempty"`
	ConfirmedAlertCount              int32                  `protobuf:"varint,16,opt,name=confirmed_alert_count,json=confirmedAlertCount,proto3" json:"confirmed_alert_count,omitempty"`
	ProbableAlertCount               int32                  `protobuf:"varint,17,opt,name=probable_alert_count,json=probableAlertCount,proto3" json:"probable_alert_count,omitempty"`
	PendingForDecision               int32                  `protobuf:"varint,18,opt,name=pending_for_decision,json=pendingForDecision,proto3" json:"pending_for_decision,omitempty"`
	NoMatchCount                     int32                  `protobuf:"varint,19,opt,name=no_match_count,json=noMatchCount,proto3" json:"no_match_count,omitempty"`
	TrueMatchCount                   int32                  `protobuf:"varint,20,opt,name=true_match_count,json=trueMatchCount,proto3" json:"true_match_count,omitempty"`
	CaseStage                        string                 `protobuf:"bytes,21,opt,name=case_stage,json=caseStage,proto3" json:"case_stage,omitempty"`
	CaseCategory                     CaseCategory           `protobuf:"varint,22,opt,name=case_category,json=caseCategory,proto3,enum=vendorgateway.aml.CaseCategory" json:"case_category,omitempty"`
	CurrentAssignee                  string                 `protobuf:"bytes,23,opt,name=current_assignee,json=currentAssignee,proto3" json:"current_assignee,omitempty"`
	CaseClosureDateTime              *timestamppb.Timestamp `protobuf:"bytes,24,opt,name=case_closure_date_time,json=caseClosureDateTime,proto3" json:"case_closure_date_time,omitempty"`
	FinalRemarks                     string                 `protobuf:"bytes,25,opt,name=final_remarks,json=finalRemarks,proto3" json:"final_remarks,omitempty"`
	CaseActions                      []*CaseAction          `protobuf:"bytes,26,rep,name=case_actions,json=caseActions,proto3" json:"case_actions,omitempty"`
	AlertDetails                     []*AlertDetails        `protobuf:"bytes,27,rep,name=alert_details,json=alertDetails,proto3" json:"alert_details,omitempty"`
}

func (x *Case) Reset() {
	*x = Case{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_case_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Case) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Case) ProtoMessage() {}

func (x *Case) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_case_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Case.ProtoReflect.Descriptor instead.
func (*Case) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_case_proto_rawDescGZIP(), []int{0}
}

func (x *Case) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *Case) GetCaseCreationDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CaseCreationDateTime
	}
	return nil
}

func (x *Case) GetSourceSystemName() string {
	if x != nil {
		return x.SourceSystemName
	}
	return ""
}

func (x *Case) GetSourceSystemCustomerCode() string {
	if x != nil {
		return x.SourceSystemCustomerCode
	}
	return ""
}

func (x *Case) GetApplicationRefNumber() string {
	if x != nil {
		return x.ApplicationRefNumber
	}
	return ""
}

func (x *Case) GetCaseOf() CaseOf {
	if x != nil {
		return x.CaseOf
	}
	return CaseOf_CASE_OF_UNSPECIFIED
}

func (x *Case) GetLinkedToSourceSystemCustomerCode() string {
	if x != nil {
		return x.LinkedToSourceSystemCustomerCode
	}
	return ""
}

func (x *Case) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

func (x *Case) GetScreeningProfile() string {
	if x != nil {
		return x.ScreeningProfile
	}
	return ""
}

func (x *Case) GetScreeningProfileName() string {
	if x != nil {
		return x.ScreeningProfileName
	}
	return ""
}

func (x *Case) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *Case) GetCaseType() CaseType {
	if x != nil {
		return x.CaseType
	}
	return CaseType_CASE_TYPE_UNSPECIFIED
}

func (x *Case) GetInitialScreeningMode() InitialScreeningMode {
	if x != nil {
		return x.InitialScreeningMode
	}
	return InitialScreeningMode_INITIAL_SCREENING_MODE_UNSPECIFIED
}

func (x *Case) GetOnboardingDecision() OnboardingDecision {
	if x != nil {
		return x.OnboardingDecision
	}
	return OnboardingDecision_ONBOARDING_DECISION_UNSPECIFIED
}

func (x *Case) GetTotalAlertCount() int32 {
	if x != nil {
		return x.TotalAlertCount
	}
	return 0
}

func (x *Case) GetConfirmedAlertCount() int32 {
	if x != nil {
		return x.ConfirmedAlertCount
	}
	return 0
}

func (x *Case) GetProbableAlertCount() int32 {
	if x != nil {
		return x.ProbableAlertCount
	}
	return 0
}

func (x *Case) GetPendingForDecision() int32 {
	if x != nil {
		return x.PendingForDecision
	}
	return 0
}

func (x *Case) GetNoMatchCount() int32 {
	if x != nil {
		return x.NoMatchCount
	}
	return 0
}

func (x *Case) GetTrueMatchCount() int32 {
	if x != nil {
		return x.TrueMatchCount
	}
	return 0
}

func (x *Case) GetCaseStage() string {
	if x != nil {
		return x.CaseStage
	}
	return ""
}

func (x *Case) GetCaseCategory() CaseCategory {
	if x != nil {
		return x.CaseCategory
	}
	return CaseCategory_CASE_CATEGORY_UNSPECIFIED
}

func (x *Case) GetCurrentAssignee() string {
	if x != nil {
		return x.CurrentAssignee
	}
	return ""
}

func (x *Case) GetCaseClosureDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CaseClosureDateTime
	}
	return nil
}

func (x *Case) GetFinalRemarks() string {
	if x != nil {
		return x.FinalRemarks
	}
	return ""
}

func (x *Case) GetCaseActions() []*CaseAction {
	if x != nil {
		return x.CaseActions
	}
	return nil
}

func (x *Case) GetAlertDetails() []*AlertDetails {
	if x != nil {
		return x.AlertDetails
	}
	return nil
}

type CaseAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName string                 `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	DateTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`
	Action   string                 `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *CaseAction) Reset() {
	*x = CaseAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_case_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseAction) ProtoMessage() {}

func (x *CaseAction) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_case_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseAction.ProtoReflect.Descriptor instead.
func (*CaseAction) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_case_proto_rawDescGZIP(), []int{1}
}

func (x *CaseAction) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *CaseAction) GetDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DateTime
	}
	return nil
}

func (x *CaseAction) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type AlertDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AlertId              string                  `protobuf:"bytes,1,opt,name=alert_id,json=alertId,proto3" json:"alert_id,omitempty"`
	Source               string                  `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	WatchlistSourceId    string                  `protobuf:"bytes,3,opt,name=watchlist_source_id,json=watchlistSourceId,proto3" json:"watchlist_source_id,omitempty"`
	MatchType            MatchType               `protobuf:"varint,4,opt,name=match_type,json=matchType,proto3,enum=vendorgateway.aml.MatchType" json:"match_type,omitempty"`
	MatchingAttributes   []string                `protobuf:"bytes,5,rep,name=matching_attributes,json=matchingAttributes,proto3" json:"matching_attributes,omitempty"`
	SourceIdentification []*SourceIdentification `protobuf:"bytes,6,rep,name=source_identification,json=sourceIdentification,proto3" json:"source_identification,omitempty"`
	WatchlistName        string                  `protobuf:"bytes,7,opt,name=watchlist_name,json=watchlistName,proto3" json:"watchlist_name,omitempty"`
	AlertDecision        AlertDecision           `protobuf:"varint,8,opt,name=alert_decision,json=alertDecision,proto3,enum=vendorgateway.aml.AlertDecision" json:"alert_decision,omitempty"`
	Comments             []*Comment              `protobuf:"bytes,9,rep,name=comments,proto3" json:"comments,omitempty"`
}

func (x *AlertDetails) Reset() {
	*x = AlertDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_case_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlertDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertDetails) ProtoMessage() {}

func (x *AlertDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_case_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertDetails.ProtoReflect.Descriptor instead.
func (*AlertDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_case_proto_rawDescGZIP(), []int{2}
}

func (x *AlertDetails) GetAlertId() string {
	if x != nil {
		return x.AlertId
	}
	return ""
}

func (x *AlertDetails) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *AlertDetails) GetWatchlistSourceId() string {
	if x != nil {
		return x.WatchlistSourceId
	}
	return ""
}

func (x *AlertDetails) GetMatchType() MatchType {
	if x != nil {
		return x.MatchType
	}
	return MatchType_MATCH_TYPE_UNSPECIFIED
}

func (x *AlertDetails) GetMatchingAttributes() []string {
	if x != nil {
		return x.MatchingAttributes
	}
	return nil
}

func (x *AlertDetails) GetSourceIdentification() []*SourceIdentification {
	if x != nil {
		return x.SourceIdentification
	}
	return nil
}

func (x *AlertDetails) GetWatchlistName() string {
	if x != nil {
		return x.WatchlistName
	}
	return ""
}

func (x *AlertDetails) GetAlertDecision() AlertDecision {
	if x != nil {
		return x.AlertDecision
	}
	return AlertDecision_ALERT_DECISION_UNSPECIFIED
}

func (x *AlertDetails) GetComments() []*Comment {
	if x != nil {
		return x.Comments
	}
	return nil
}

type SourceIdentification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceIdentificationId    string `protobuf:"bytes,1,opt,name=source_identification_id,json=sourceIdentificationId,proto3" json:"source_identification_id,omitempty"`
	SourceIdentificationKey   string `protobuf:"bytes,2,opt,name=source_identification_key,json=sourceIdentificationKey,proto3" json:"source_identification_key,omitempty"`
	SourceIdentificationValue string `protobuf:"bytes,3,opt,name=source_identification_value,json=sourceIdentificationValue,proto3" json:"source_identification_value,omitempty"`
}

func (x *SourceIdentification) Reset() {
	*x = SourceIdentification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_case_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceIdentification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceIdentification) ProtoMessage() {}

func (x *SourceIdentification) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_case_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceIdentification.ProtoReflect.Descriptor instead.
func (*SourceIdentification) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_case_proto_rawDescGZIP(), []int{3}
}

func (x *SourceIdentification) GetSourceIdentificationId() string {
	if x != nil {
		return x.SourceIdentificationId
	}
	return ""
}

func (x *SourceIdentification) GetSourceIdentificationKey() string {
	if x != nil {
		return x.SourceIdentificationKey
	}
	return ""
}

func (x *SourceIdentification) GetSourceIdentificationValue() string {
	if x != nil {
		return x.SourceIdentificationValue
	}
	return ""
}

type Comment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName string                 `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	DateTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`
	Comment  string                 `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`
}

func (x *Comment) Reset() {
	*x = Comment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_case_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Comment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Comment) ProtoMessage() {}

func (x *Comment) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_case_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Comment.ProtoReflect.Descriptor instead.
func (*Comment) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_case_proto_rawDescGZIP(), []int{4}
}

func (x *Comment) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *Comment) GetDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DateTime
	}
	return nil
}

func (x *Comment) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

var File_api_vendorgateway_aml_case_proto protoreflect.FileDescriptor

var file_api_vendorgateway_aml_case_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd1, 0x0b, 0x0a, 0x04, 0x43,
	0x61, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x17,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x63, 0x61, 0x73, 0x65, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x2c, 0x0a, 0x12, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a,
	0x1b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x18, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x16,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x32, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6f, 0x66, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x4f, 0x66, 0x52, 0x06,
	0x63, 0x61, 0x73, 0x65, 0x4f, 0x66, 0x12, 0x4f, 0x0a, 0x25, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64,
	0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x20, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x54, 0x6f, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x34, 0x0a, 0x16, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61,
	0x6d, 0x6c, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x61, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5d, 0x0a, 0x16, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x14,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x13, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x65, 0x64, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14,
	0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x62,
	0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30,
	0x0a, 0x14, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x64, 0x65,
	0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x70, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x6f, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6e, 0x6f, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x72, 0x75, 0x65, 0x5f, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x74, 0x72, 0x75, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x44, 0x0a, 0x0d, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0c, 0x63, 0x61, 0x73, 0x65, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x65,
	0x12, 0x4f, 0x0a, 0x16, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x13, 0x63, 0x61,
	0x73, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x52,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x40, 0x0a, 0x0c, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c,
	0x2e, 0x43, 0x61, 0x73, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x61, 0x73,
	0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x44, 0x0a, 0x0d, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x0c, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x7a,
	0x0a, 0x0a, 0x43, 0x61, 0x73, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe5, 0x03, 0x0a, 0x0c, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x77, 0x61, 0x74,
	0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3b,
	0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x69,
	0x6e, 0x67, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x15,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x77, 0x61,
	0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x47, 0x0a, 0x0e, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x63, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x08, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x22, 0xcc, 0x01, 0x0a, 0x14, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x18, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x19, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4b, 0x65,
	0x79, 0x12, 0x3e, 0x0a, 0x1b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x79, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x2a, 0x57, 0x0a, 0x06,
	0x43, 0x61, 0x73, 0x65, 0x4f, 0x66, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x4f,
	0x46, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x14, 0x0a, 0x10, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x4f, 0x46, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x5f,
	0x4b, 0x59, 0x43, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x4f, 0x46,
	0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x5f,
	0x4b, 0x59, 0x43, 0x10, 0x02, 0x2a, 0x89, 0x01, 0x0a, 0x14, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x26,
	0x0a, 0x22, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41,
	0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x5f, 0x41, 0x50, 0x49, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41,
	0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x5f, 0x4c, 0x4f, 0x4f, 0x4b, 0x55, 0x50, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x10,
	0x02, 0x2a, 0x7b, 0x0a, 0x12, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x1f, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53,
	0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1f, 0x0a,
	0x1b, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x43, 0x49,
	0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x02, 0x2a, 0x87,
	0x01, 0x0a, 0x0d, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1d, 0x0a, 0x19, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x55, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x01, 0x12,
	0x1b, 0x0a, 0x17, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x4e, 0x4f, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16,
	0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x50,
	0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x61, 0x6d, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_aml_case_proto_rawDescOnce sync.Once
	file_api_vendorgateway_aml_case_proto_rawDescData = file_api_vendorgateway_aml_case_proto_rawDesc
)

func file_api_vendorgateway_aml_case_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_aml_case_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_aml_case_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_aml_case_proto_rawDescData)
	})
	return file_api_vendorgateway_aml_case_proto_rawDescData
}

var file_api_vendorgateway_aml_case_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_vendorgateway_aml_case_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_vendorgateway_aml_case_proto_goTypes = []interface{}{
	(CaseOf)(0),                   // 0: vendorgateway.aml.CaseOf
	(InitialScreeningMode)(0),     // 1: vendorgateway.aml.InitialScreeningMode
	(OnboardingDecision)(0),       // 2: vendorgateway.aml.OnboardingDecision
	(AlertDecision)(0),            // 3: vendorgateway.aml.AlertDecision
	(*Case)(nil),                  // 4: vendorgateway.aml.Case
	(*CaseAction)(nil),            // 5: vendorgateway.aml.CaseAction
	(*AlertDetails)(nil),          // 6: vendorgateway.aml.AlertDetails
	(*SourceIdentification)(nil),  // 7: vendorgateway.aml.SourceIdentification
	(*Comment)(nil),               // 8: vendorgateway.aml.Comment
	(*timestamppb.Timestamp)(nil), // 9: google.protobuf.Timestamp
	(CaseType)(0),                 // 10: vendorgateway.aml.CaseType
	(CaseCategory)(0),             // 11: vendorgateway.aml.CaseCategory
	(MatchType)(0),                // 12: vendorgateway.aml.MatchType
}
var file_api_vendorgateway_aml_case_proto_depIdxs = []int32{
	9,  // 0: vendorgateway.aml.Case.case_creation_date_time:type_name -> google.protobuf.Timestamp
	0,  // 1: vendorgateway.aml.Case.case_of:type_name -> vendorgateway.aml.CaseOf
	10, // 2: vendorgateway.aml.Case.case_type:type_name -> vendorgateway.aml.CaseType
	1,  // 3: vendorgateway.aml.Case.initial_screening_mode:type_name -> vendorgateway.aml.InitialScreeningMode
	2,  // 4: vendorgateway.aml.Case.onboarding_decision:type_name -> vendorgateway.aml.OnboardingDecision
	11, // 5: vendorgateway.aml.Case.case_category:type_name -> vendorgateway.aml.CaseCategory
	9,  // 6: vendorgateway.aml.Case.case_closure_date_time:type_name -> google.protobuf.Timestamp
	5,  // 7: vendorgateway.aml.Case.case_actions:type_name -> vendorgateway.aml.CaseAction
	6,  // 8: vendorgateway.aml.Case.alert_details:type_name -> vendorgateway.aml.AlertDetails
	9,  // 9: vendorgateway.aml.CaseAction.date_time:type_name -> google.protobuf.Timestamp
	12, // 10: vendorgateway.aml.AlertDetails.match_type:type_name -> vendorgateway.aml.MatchType
	7,  // 11: vendorgateway.aml.AlertDetails.source_identification:type_name -> vendorgateway.aml.SourceIdentification
	3,  // 12: vendorgateway.aml.AlertDetails.alert_decision:type_name -> vendorgateway.aml.AlertDecision
	8,  // 13: vendorgateway.aml.AlertDetails.comments:type_name -> vendorgateway.aml.Comment
	9,  // 14: vendorgateway.aml.Comment.date_time:type_name -> google.protobuf.Timestamp
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_aml_case_proto_init() }
func file_api_vendorgateway_aml_case_proto_init() {
	if File_api_vendorgateway_aml_case_proto != nil {
		return
	}
	file_api_vendorgateway_aml_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_aml_case_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Case); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_case_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_case_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlertDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_case_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceIdentification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_case_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Comment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_aml_case_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_aml_case_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_aml_case_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_aml_case_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_aml_case_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_aml_case_proto = out.File
	file_api_vendorgateway_aml_case_proto_rawDesc = nil
	file_api_vendorgateway_aml_case_proto_goTypes = nil
	file_api_vendorgateway_aml_case_proto_depIdxs = nil
}
