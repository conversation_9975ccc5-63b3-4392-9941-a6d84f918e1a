//go:generate gen_sql -types=PolicyIssuanceRequestStatus,HealthInsurancePolicyType
syntax = "proto3";

package salaryprogram.healthinsurance;

import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";


option go_package = "github.com/epifi/gamma/api/salaryprogram/healthinsurance";
option java_package = "com.github.epifi.gamma.api.salaryprogram.healthinsurance";

// HealthInsurancePolicyIssuanceRequest stores the details of request needed for initiating a health insurance policy purchase.
message HealthInsurancePolicyIssuanceRequest {
  // unique id of issuance request.
  string id = 1;
  // denotes the actor for whom request was created.
  string actor_id = 2;
  // denotes the vendor who would be fulfilling the policy request.
  vendorgateway.Vendor policy_vendor = 3;
  // denotes the unique request id passed to vendor while purchasing the policy.
  string vendor_request_id = 4;
  // denotes the status of issuance request.
  PolicyIssuanceRequestStatus request_status = 5;
  // denotes the time until which the policy issuance request is valid,
  // if the callback for policy purchase confirmation comes after this time then it should be rejected.
  google.protobuf.Timestamp request_expires_at = 6;
  // Enum denotes the policy type of health insurance for which request is created.
  HealthInsurancePolicyType policy_type = 7;

  google.protobuf.Timestamp created_at = 14;
  google.protobuf.Timestamp updated_at = 15;
}

enum PolicyIssuanceRequestStatus {
  POLICY_ISSUANCE_REQUEST_STATUS_UNSPECIFIED = 0;
  REQUEST_STATUS_CREATED = 1;
  REQUEST_STATUS_VENDOR_PURCHASE_IN_PROGRESS = 2;
  REQUEST_STATUS_VENDOR_PURCHASE_SUCCESSFUL = 3;
  REQUEST_STATUS_VENDOR_PURCHASE_FAILED = 4;
}

enum HealthInsurancePolicyIssuanceRequestFieldMask {
  POLICY_ISSUANCE_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  REQUEST_STATUS = 1;
}

// https://docs.google.com/spreadsheets/d/1ArS49J91TfqFLbw2-U32FfF1rj6wE57XHfyKNLV1Jfw/edit?gid=0#gid=0 for updated policies
// Steps to follow to add a new policy:
// - update enum in api/typesv2/salaryprogram/health_insurance.proto
// - create reward offer for the newly added policy. check 95711cae-a985-47df-9f80-c27de9a8059f reward offer for referance
// - handle new enums in FE and BE RPCs
// - update salaryprogram/healthinsurance/service.go -> getVendorBasedOnUserEmployerChannelAndPolicyType method for every newly added health insruance policy
enum HealthInsurancePolicyType {
  HEALTH_INSURANCE_POLICY_TYPE_UNSPECIFIED = 0;
  BASE_HEALTH_INSURANCE = 1;
  SUPER_TOP_UP_INSURANCE = 2;
  ONSURITY_OPD_WELLNESS_2A2C = 3;
  ONSURITY_OPD_WELLNESS_2A = 4;
  ONSURITY_OPD_WELLNESS_1A = 5;
  ONSURITY_DIAMOND_PLUS_1A = 6;
  // GPA - Group Personal Accident insurance
  // GHI - Group Health Insurance
  ONSURITY_GHI_GPA_RUBY_1A = 7;
  ONSURITY_GHI_GPA_OPAL_1A = 8;
}
