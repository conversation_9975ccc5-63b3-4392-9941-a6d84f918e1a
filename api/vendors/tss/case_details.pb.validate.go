// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/tss/case_details.proto

package tss

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CaseDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CaseDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CaseDetailsMultiError, or
// nil if none found.
func (m *CaseDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseId

	// no validation rules for CaseCreationDateTimeInUtc

	// no validation rules for SourceSystemName

	// no validation rules for SourceSystemCustomerCode

	// no validation rules for ApplicationRefNumber

	// no validation rules for CaseOf

	// no validation rules for LinkedToSourceSystemCustomerCode

	// no validation rules for Relation

	// no validation rules for ScreeningProfile

	// no validation rules for ScreeningProfileName

	// no validation rules for CustomerName

	// no validation rules for CaseType

	// no validation rules for InitialScreeningMode

	// no validation rules for OnboardingDecision

	// no validation rules for TotalAlertCount

	// no validation rules for ConfirmedAlertCount

	// no validation rules for ProbableAlertCount

	// no validation rules for PendingForDecision

	// no validation rules for NoMatchCount

	// no validation rules for TrueMatchCount

	// no validation rules for CaseStage

	// no validation rules for CaseCategory

	// no validation rules for CurrentAssignee

	// no validation rules for CaseClosureDateTimeInUtc

	// no validation rules for FinalRemarks

	for idx, item := range m.GetCaseActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CaseDetailsValidationError{
						field:  fmt.Sprintf("CaseActions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CaseDetailsValidationError{
						field:  fmt.Sprintf("CaseActions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CaseDetailsValidationError{
					field:  fmt.Sprintf("CaseActions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAlertDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CaseDetailsValidationError{
						field:  fmt.Sprintf("AlertDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CaseDetailsValidationError{
						field:  fmt.Sprintf("AlertDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CaseDetailsValidationError{
					field:  fmt.Sprintf("AlertDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CaseDetailsMultiError(errors)
	}

	return nil
}

// CaseDetailsMultiError is an error wrapping multiple validation errors
// returned by CaseDetails.ValidateAll() if the designated constraints aren't met.
type CaseDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseDetailsMultiError) AllErrors() []error { return m }

// CaseDetailsValidationError is the validation error returned by
// CaseDetails.Validate if the designated constraints aren't met.
type CaseDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseDetailsValidationError) ErrorName() string { return "CaseDetailsValidationError" }

// Error satisfies the builtin error interface
func (e CaseDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseDetailsValidationError{}

// Validate checks the field values on CaseAction with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CaseAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseAction with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CaseActionMultiError, or
// nil if none found.
func (m *CaseAction) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserName

	// no validation rules for DateTimeInUtc

	// no validation rules for Action

	if len(errors) > 0 {
		return CaseActionMultiError(errors)
	}

	return nil
}

// CaseActionMultiError is an error wrapping multiple validation errors
// returned by CaseAction.ValidateAll() if the designated constraints aren't met.
type CaseActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseActionMultiError) AllErrors() []error { return m }

// CaseActionValidationError is the validation error returned by
// CaseAction.Validate if the designated constraints aren't met.
type CaseActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseActionValidationError) ErrorName() string { return "CaseActionValidationError" }

// Error satisfies the builtin error interface
func (e CaseActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseActionValidationError{}

// Validate checks the field values on AlertDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AlertDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AlertDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AlertDetailsMultiError, or
// nil if none found.
func (m *AlertDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AlertDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AlertId

	// no validation rules for Source

	// no validation rules for WatchlistSourceId

	// no validation rules for MatchType

	for idx, item := range m.GetSourceIdentification() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AlertDetailsValidationError{
						field:  fmt.Sprintf("SourceIdentification[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AlertDetailsValidationError{
						field:  fmt.Sprintf("SourceIdentification[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AlertDetailsValidationError{
					field:  fmt.Sprintf("SourceIdentification[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for WatchlistName

	// no validation rules for AlertDecision

	for idx, item := range m.GetComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AlertDetailsValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AlertDetailsValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AlertDetailsValidationError{
					field:  fmt.Sprintf("Comments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AlertDetailsMultiError(errors)
	}

	return nil
}

// AlertDetailsMultiError is an error wrapping multiple validation errors
// returned by AlertDetails.ValidateAll() if the designated constraints aren't met.
type AlertDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AlertDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AlertDetailsMultiError) AllErrors() []error { return m }

// AlertDetailsValidationError is the validation error returned by
// AlertDetails.Validate if the designated constraints aren't met.
type AlertDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AlertDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AlertDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AlertDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AlertDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AlertDetailsValidationError) ErrorName() string { return "AlertDetailsValidationError" }

// Error satisfies the builtin error interface
func (e AlertDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAlertDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AlertDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AlertDetailsValidationError{}

// Validate checks the field values on SourceIdentification with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SourceIdentification) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SourceIdentification with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SourceIdentificationMultiError, or nil if none found.
func (m *SourceIdentification) ValidateAll() error {
	return m.validate(true)
}

func (m *SourceIdentification) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceIdentificationId

	// no validation rules for SourceIdentificationKey

	// no validation rules for SourceIdentificationValue

	if len(errors) > 0 {
		return SourceIdentificationMultiError(errors)
	}

	return nil
}

// SourceIdentificationMultiError is an error wrapping multiple validation
// errors returned by SourceIdentification.ValidateAll() if the designated
// constraints aren't met.
type SourceIdentificationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SourceIdentificationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SourceIdentificationMultiError) AllErrors() []error { return m }

// SourceIdentificationValidationError is the validation error returned by
// SourceIdentification.Validate if the designated constraints aren't met.
type SourceIdentificationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SourceIdentificationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SourceIdentificationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SourceIdentificationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SourceIdentificationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SourceIdentificationValidationError) ErrorName() string {
	return "SourceIdentificationValidationError"
}

// Error satisfies the builtin error interface
func (e SourceIdentificationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSourceIdentification.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SourceIdentificationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SourceIdentificationValidationError{}

// Validate checks the field values on Comment with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Comment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Comment with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CommentMultiError, or nil if none found.
func (m *Comment) ValidateAll() error {
	return m.validate(true)
}

func (m *Comment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserName

	// no validation rules for DateTimeInUtc

	// no validation rules for Comment

	if len(errors) > 0 {
		return CommentMultiError(errors)
	}

	return nil
}

// CommentMultiError is an error wrapping multiple validation errors returned
// by Comment.ValidateAll() if the designated constraints aren't met.
type CommentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommentMultiError) AllErrors() []error { return m }

// CommentValidationError is the validation error returned by Comment.Validate
// if the designated constraints aren't met.
type CommentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommentValidationError) ErrorName() string { return "CommentValidationError" }

// Error satisfies the builtin error interface
func (e CommentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommentValidationError{}
