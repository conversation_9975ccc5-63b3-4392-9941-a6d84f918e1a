// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/tss/case_details.proto

package tss

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CaseDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseId                           string          `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	CaseCreationDateTimeInUtc        string          `protobuf:"bytes,2,opt,name=case_creation_date_time_in_utc,json=caseCreationDateTimeInUTC,proto3" json:"case_creation_date_time_in_utc,omitempty"`
	SourceSystemName                 string          `protobuf:"bytes,3,opt,name=source_system_name,json=sourceSystemName,proto3" json:"source_system_name,omitempty"`
	SourceSystemCustomerCode         string          `protobuf:"bytes,4,opt,name=source_system_customer_code,json=sourceSystemCustomerCode,proto3" json:"source_system_customer_code,omitempty"`
	ApplicationRefNumber             string          `protobuf:"bytes,5,opt,name=application_ref_number,json=applicationRefNumber,proto3" json:"application_ref_number,omitempty"`
	CaseOf                           string          `protobuf:"bytes,6,opt,name=case_of,json=caseOf,proto3" json:"case_of,omitempty"`
	LinkedToSourceSystemCustomerCode string          `protobuf:"bytes,7,opt,name=linked_to_source_system_customer_code,json=linkedToSourceSystemCustomerCode,proto3" json:"linked_to_source_system_customer_code,omitempty"`
	Relation                         string          `protobuf:"bytes,8,opt,name=relation,proto3" json:"relation,omitempty"`
	ScreeningProfile                 string          `protobuf:"bytes,9,opt,name=screening_profile,json=screeningProfile,proto3" json:"screening_profile,omitempty"`
	ScreeningProfileName             string          `protobuf:"bytes,10,opt,name=screening_profile_name,json=screeningProfileName,proto3" json:"screening_profile_name,omitempty"`
	CustomerName                     string          `protobuf:"bytes,11,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	CaseType                         string          `protobuf:"bytes,12,opt,name=case_type,json=caseType,proto3" json:"case_type,omitempty"`
	InitialScreeningMode             string          `protobuf:"bytes,13,opt,name=initial_screening_mode,json=initialScreeningMode,proto3" json:"initial_screening_mode,omitempty"`
	OnboardingDecision               string          `protobuf:"bytes,14,opt,name=onboarding_decision,json=onboardingDecision,proto3" json:"onboarding_decision,omitempty"`
	TotalAlertCount                  int32           `protobuf:"varint,15,opt,name=total_alert_count,json=totalAlertCount,proto3" json:"total_alert_count,omitempty"`
	ConfirmedAlertCount              int32           `protobuf:"varint,16,opt,name=confirmed_alert_count,json=confirmedAlertCount,proto3" json:"confirmed_alert_count,omitempty"`
	ProbableAlertCount               int32           `protobuf:"varint,17,opt,name=probable_alert_count,json=probableAlertCount,proto3" json:"probable_alert_count,omitempty"`
	PendingForDecision               int32           `protobuf:"varint,18,opt,name=pending_for_decision,json=pendingForDecision,proto3" json:"pending_for_decision,omitempty"`
	NoMatchCount                     int32           `protobuf:"varint,19,opt,name=no_match_count,json=noMatchCount,proto3" json:"no_match_count,omitempty"`
	TrueMatchCount                   int32           `protobuf:"varint,20,opt,name=true_match_count,json=trueMatchCount,proto3" json:"true_match_count,omitempty"`
	CaseStage                        string          `protobuf:"bytes,21,opt,name=case_stage,json=caseStage,proto3" json:"case_stage,omitempty"`
	CaseCategory                     string          `protobuf:"bytes,22,opt,name=case_category,json=caseCategory,proto3" json:"case_category,omitempty"`
	CurrentAssignee                  string          `protobuf:"bytes,23,opt,name=current_assignee,json=currentAssignee,proto3" json:"current_assignee,omitempty"`
	CaseClosureDateTimeInUtc         string          `protobuf:"bytes,24,opt,name=case_closure_date_time_in_utc,json=caseClosureDateTimeInUTC,proto3" json:"case_closure_date_time_in_utc,omitempty"`
	FinalRemarks                     string          `protobuf:"bytes,25,opt,name=final_remarks,json=finalRemarks,proto3" json:"final_remarks,omitempty"`
	CaseActions                      []*CaseAction   `protobuf:"bytes,26,rep,name=case_actions,json=caseActions,proto3" json:"case_actions,omitempty"`
	AlertDetails                     []*AlertDetails `protobuf:"bytes,27,rep,name=alert_details,json=alertDetails,proto3" json:"alert_details,omitempty"`
}

func (x *CaseDetails) Reset() {
	*x = CaseDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_case_details_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseDetails) ProtoMessage() {}

func (x *CaseDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_case_details_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseDetails.ProtoReflect.Descriptor instead.
func (*CaseDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_case_details_proto_rawDescGZIP(), []int{0}
}

func (x *CaseDetails) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CaseDetails) GetCaseCreationDateTimeInUtc() string {
	if x != nil {
		return x.CaseCreationDateTimeInUtc
	}
	return ""
}

func (x *CaseDetails) GetSourceSystemName() string {
	if x != nil {
		return x.SourceSystemName
	}
	return ""
}

func (x *CaseDetails) GetSourceSystemCustomerCode() string {
	if x != nil {
		return x.SourceSystemCustomerCode
	}
	return ""
}

func (x *CaseDetails) GetApplicationRefNumber() string {
	if x != nil {
		return x.ApplicationRefNumber
	}
	return ""
}

func (x *CaseDetails) GetCaseOf() string {
	if x != nil {
		return x.CaseOf
	}
	return ""
}

func (x *CaseDetails) GetLinkedToSourceSystemCustomerCode() string {
	if x != nil {
		return x.LinkedToSourceSystemCustomerCode
	}
	return ""
}

func (x *CaseDetails) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

func (x *CaseDetails) GetScreeningProfile() string {
	if x != nil {
		return x.ScreeningProfile
	}
	return ""
}

func (x *CaseDetails) GetScreeningProfileName() string {
	if x != nil {
		return x.ScreeningProfileName
	}
	return ""
}

func (x *CaseDetails) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *CaseDetails) GetCaseType() string {
	if x != nil {
		return x.CaseType
	}
	return ""
}

func (x *CaseDetails) GetInitialScreeningMode() string {
	if x != nil {
		return x.InitialScreeningMode
	}
	return ""
}

func (x *CaseDetails) GetOnboardingDecision() string {
	if x != nil {
		return x.OnboardingDecision
	}
	return ""
}

func (x *CaseDetails) GetTotalAlertCount() int32 {
	if x != nil {
		return x.TotalAlertCount
	}
	return 0
}

func (x *CaseDetails) GetConfirmedAlertCount() int32 {
	if x != nil {
		return x.ConfirmedAlertCount
	}
	return 0
}

func (x *CaseDetails) GetProbableAlertCount() int32 {
	if x != nil {
		return x.ProbableAlertCount
	}
	return 0
}

func (x *CaseDetails) GetPendingForDecision() int32 {
	if x != nil {
		return x.PendingForDecision
	}
	return 0
}

func (x *CaseDetails) GetNoMatchCount() int32 {
	if x != nil {
		return x.NoMatchCount
	}
	return 0
}

func (x *CaseDetails) GetTrueMatchCount() int32 {
	if x != nil {
		return x.TrueMatchCount
	}
	return 0
}

func (x *CaseDetails) GetCaseStage() string {
	if x != nil {
		return x.CaseStage
	}
	return ""
}

func (x *CaseDetails) GetCaseCategory() string {
	if x != nil {
		return x.CaseCategory
	}
	return ""
}

func (x *CaseDetails) GetCurrentAssignee() string {
	if x != nil {
		return x.CurrentAssignee
	}
	return ""
}

func (x *CaseDetails) GetCaseClosureDateTimeInUtc() string {
	if x != nil {
		return x.CaseClosureDateTimeInUtc
	}
	return ""
}

func (x *CaseDetails) GetFinalRemarks() string {
	if x != nil {
		return x.FinalRemarks
	}
	return ""
}

func (x *CaseDetails) GetCaseActions() []*CaseAction {
	if x != nil {
		return x.CaseActions
	}
	return nil
}

func (x *CaseDetails) GetAlertDetails() []*AlertDetails {
	if x != nil {
		return x.AlertDetails
	}
	return nil
}

type CaseAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName      string `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	DateTimeInUtc string `protobuf:"bytes,2,opt,name=date_time_in_utc,json=dateTimeInUTC,proto3" json:"date_time_in_utc,omitempty"`
	Action        string `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *CaseAction) Reset() {
	*x = CaseAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_case_details_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseAction) ProtoMessage() {}

func (x *CaseAction) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_case_details_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseAction.ProtoReflect.Descriptor instead.
func (*CaseAction) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_case_details_proto_rawDescGZIP(), []int{1}
}

func (x *CaseAction) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *CaseAction) GetDateTimeInUtc() string {
	if x != nil {
		return x.DateTimeInUtc
	}
	return ""
}

func (x *CaseAction) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type AlertDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AlertId              string                  `protobuf:"bytes,1,opt,name=alert_id,json=alertId,proto3" json:"alert_id,omitempty"`
	Source               string                  `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	WatchlistSourceId    string                  `protobuf:"bytes,3,opt,name=watchlist_source_id,json=watchlistSourceId,proto3" json:"watchlist_source_id,omitempty"`
	MatchType            string                  `protobuf:"bytes,4,opt,name=match_type,json=matchType,proto3" json:"match_type,omitempty"`
	MatchingAttributes   []string                `protobuf:"bytes,5,rep,name=matching_attributes,json=matchingAttributes,proto3" json:"matching_attributes,omitempty"`
	SourceIdentification []*SourceIdentification `protobuf:"bytes,6,rep,name=source_identification,json=sourceIdentification,proto3" json:"source_identification,omitempty"`
	WatchlistName        string                  `protobuf:"bytes,7,opt,name=watchlist_name,json=watchlistName,proto3" json:"watchlist_name,omitempty"`
	AlertDecision        string                  `protobuf:"bytes,8,opt,name=alert_decision,json=alertDecision,proto3" json:"alert_decision,omitempty"`
	Comments             []*Comment              `protobuf:"bytes,9,rep,name=comments,proto3" json:"comments,omitempty"`
}

func (x *AlertDetails) Reset() {
	*x = AlertDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_case_details_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlertDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertDetails) ProtoMessage() {}

func (x *AlertDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_case_details_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertDetails.ProtoReflect.Descriptor instead.
func (*AlertDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_case_details_proto_rawDescGZIP(), []int{2}
}

func (x *AlertDetails) GetAlertId() string {
	if x != nil {
		return x.AlertId
	}
	return ""
}

func (x *AlertDetails) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *AlertDetails) GetWatchlistSourceId() string {
	if x != nil {
		return x.WatchlistSourceId
	}
	return ""
}

func (x *AlertDetails) GetMatchType() string {
	if x != nil {
		return x.MatchType
	}
	return ""
}

func (x *AlertDetails) GetMatchingAttributes() []string {
	if x != nil {
		return x.MatchingAttributes
	}
	return nil
}

func (x *AlertDetails) GetSourceIdentification() []*SourceIdentification {
	if x != nil {
		return x.SourceIdentification
	}
	return nil
}

func (x *AlertDetails) GetWatchlistName() string {
	if x != nil {
		return x.WatchlistName
	}
	return ""
}

func (x *AlertDetails) GetAlertDecision() string {
	if x != nil {
		return x.AlertDecision
	}
	return ""
}

func (x *AlertDetails) GetComments() []*Comment {
	if x != nil {
		return x.Comments
	}
	return nil
}

type SourceIdentification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceIdentificationId    string `protobuf:"bytes,1,opt,name=source_identification_id,json=sourceIdentificationID,proto3" json:"source_identification_id,omitempty"`
	SourceIdentificationKey   string `protobuf:"bytes,2,opt,name=source_identification_key,json=sourceIdentificationKey,proto3" json:"source_identification_key,omitempty"`
	SourceIdentificationValue string `protobuf:"bytes,3,opt,name=source_identification_value,json=sourceIdentificationValue,proto3" json:"source_identification_value,omitempty"`
}

func (x *SourceIdentification) Reset() {
	*x = SourceIdentification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_case_details_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceIdentification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceIdentification) ProtoMessage() {}

func (x *SourceIdentification) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_case_details_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceIdentification.ProtoReflect.Descriptor instead.
func (*SourceIdentification) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_case_details_proto_rawDescGZIP(), []int{3}
}

func (x *SourceIdentification) GetSourceIdentificationId() string {
	if x != nil {
		return x.SourceIdentificationId
	}
	return ""
}

func (x *SourceIdentification) GetSourceIdentificationKey() string {
	if x != nil {
		return x.SourceIdentificationKey
	}
	return ""
}

func (x *SourceIdentification) GetSourceIdentificationValue() string {
	if x != nil {
		return x.SourceIdentificationValue
	}
	return ""
}

type Comment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName      string `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	DateTimeInUtc string `protobuf:"bytes,2,opt,name=date_time_in_utc,json=dateTimeInUTC,proto3" json:"date_time_in_utc,omitempty"`
	Comment       string `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`
}

func (x *Comment) Reset() {
	*x = Comment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_case_details_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Comment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Comment) ProtoMessage() {}

func (x *Comment) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_case_details_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Comment.ProtoReflect.Descriptor instead.
func (*Comment) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_case_details_proto_rawDescGZIP(), []int{4}
}

func (x *Comment) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *Comment) GetDateTimeInUtc() string {
	if x != nil {
		return x.DateTimeInUtc
	}
	return ""
}

func (x *Comment) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

var File_api_vendors_tss_case_details_proto protoreflect.FileDescriptor

var file_api_vendors_tss_case_details_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73,
	0x73, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73,
	0x73, 0x22, 0x83, 0x0a, 0x0a, 0x0b, 0x43, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x1e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x75, 0x74, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x19, 0x63, 0x61, 0x73, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x55, 0x54, 0x43, 0x12, 0x2c, 0x0a,
	0x12, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x1b, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x18, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6f, 0x66, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x4f, 0x66, 0x12, 0x4f, 0x0a, 0x25, 0x6c, 0x69, 0x6e,
	0x6b, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x20, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64,
	0x54, 0x6f, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x2f, 0x0a, 0x13, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32,
	0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x12, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x66, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x12, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x44, 0x65,
	0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x6f, 0x5f, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x6e, 0x6f, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10,
	0x74, 0x72, 0x75, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x72, 0x75, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61,
	0x73, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x65, 0x12, 0x3f, 0x0a, 0x1d, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x69, 0x6e, 0x5f, 0x75, 0x74, 0x63, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x63, 0x61,
	0x73, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x49, 0x6e, 0x55, 0x54, 0x43, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66,
	0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e,
	0x43, 0x61, 0x73, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x61, 0x73, 0x65,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x6a, 0x0a, 0x0a, 0x43, 0x61, 0x73, 0x65, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x27, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x69, 0x6e, 0x5f, 0x75, 0x74, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x55, 0x54, 0x43, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x99, 0x03, 0x0a, 0x0c, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x77, 0x61, 0x74, 0x63, 0x68,
	0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x74, 0x73, 0x73, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x25, 0x0a, 0x0e, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f,
	0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a,
	0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22,
	0xcc, 0x01, 0x0a, 0x14, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x18, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x44, 0x12, 0x3a, 0x0a, 0x19, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x3e,
	0x0a, 0x1b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x19, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x69,
	0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x75, 0x74, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x55, 0x54, 0x43, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x50, 0x0a, 0x26, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x74, 0x73, 0x73, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_tss_case_details_proto_rawDescOnce sync.Once
	file_api_vendors_tss_case_details_proto_rawDescData = file_api_vendors_tss_case_details_proto_rawDesc
)

func file_api_vendors_tss_case_details_proto_rawDescGZIP() []byte {
	file_api_vendors_tss_case_details_proto_rawDescOnce.Do(func() {
		file_api_vendors_tss_case_details_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_tss_case_details_proto_rawDescData)
	})
	return file_api_vendors_tss_case_details_proto_rawDescData
}

var file_api_vendors_tss_case_details_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_vendors_tss_case_details_proto_goTypes = []interface{}{
	(*CaseDetails)(nil),          // 0: vendors.tss.CaseDetails
	(*CaseAction)(nil),           // 1: vendors.tss.CaseAction
	(*AlertDetails)(nil),         // 2: vendors.tss.AlertDetails
	(*SourceIdentification)(nil), // 3: vendors.tss.SourceIdentification
	(*Comment)(nil),              // 4: vendors.tss.Comment
}
var file_api_vendors_tss_case_details_proto_depIdxs = []int32{
	1, // 0: vendors.tss.CaseDetails.case_actions:type_name -> vendors.tss.CaseAction
	2, // 1: vendors.tss.CaseDetails.alert_details:type_name -> vendors.tss.AlertDetails
	3, // 2: vendors.tss.AlertDetails.source_identification:type_name -> vendors.tss.SourceIdentification
	4, // 3: vendors.tss.AlertDetails.comments:type_name -> vendors.tss.Comment
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_vendors_tss_case_details_proto_init() }
func file_api_vendors_tss_case_details_proto_init() {
	if File_api_vendors_tss_case_details_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_tss_case_details_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_case_details_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_case_details_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlertDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_case_details_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceIdentification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_case_details_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Comment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_tss_case_details_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_tss_case_details_proto_goTypes,
		DependencyIndexes: file_api_vendors_tss_case_details_proto_depIdxs,
		MessageInfos:      file_api_vendors_tss_case_details_proto_msgTypes,
	}.Build()
	File_api_vendors_tss_case_details_proto = out.File
	file_api_vendors_tss_case_details_proto_rawDesc = nil
	file_api_vendors_tss_case_details_proto_goTypes = nil
	file_api_vendors_tss_case_details_proto_depIdxs = nil
}
