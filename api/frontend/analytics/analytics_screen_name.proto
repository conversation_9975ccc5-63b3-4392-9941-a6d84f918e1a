syntax = "proto3";

package frontend.analytics;

option go_package = "github.com/epifi/gamma/api/frontend/analytics";
option java_package = "com.github.epifi.gamma.api.frontend.analytics";

// An enum to identify all the important/relevant screens in client apps. This enum is used mainly in client-only
// scenarios to keep track of the entry point of various screens. Keeping the screen names in a common enum in protos
// would enable different clients to have same contract, and also allow Backend services to utilise the same enum, when
// needed
// Details: https://docs.google.com/document/d/1iYGZGLv3BPcFBcG0SsJSLm461go_mLpPGKwNp02WxRg/edit
enum AnalyticsScreenName {
  ANALYTICS_SCREEN_NAME_UNSPECIFIED = 0;
  // Landing screen for Analyser
  ANALYZER = 1;
  // Askfi/Search results screen
  ASKFI_SEARCH_RESULTS = 2;
  // Home landing screen
  HOME_LANDING = 3;
  // The Savings account/Fi account Tab shown in the Home pull down UI
  HOME_FI_ACCOUNT_TAB = 4;
  // The connected account tab shown in the Home pull down UI
  HOME_CONNECTED_ACCOUNT_TAB = 5;
  // The Smart deposits tab shown in Home Pull Down UI
  HOME_SMART_DEPOSIT_TAB = 6;
  // The Fixed Deposits tab shown in Home Pull Down UI
  HOME_FIXED_DEPOSIT_TAB = 7;
  // The P2P investments/Jump tab shown in Home Pull Down UI
  HOME_P2P_JUMP_TAB = 8;
  // The Fi account details screen, opened when user taps on the Home Saving account title
  HOME_FI_ACCOUNT_DETAILS = 9;
  // The Pay module landing screen
  PAY_LANDING = 10;
  // Help module landing screen
  HELP_LANDING = 11;
  // Fittt module landing screen
  FITTT_LANDING = 12;
  // Profile module landing screen
  PROFILE_LANDING = 13;
  // Profile setting screen
  PROFILE_SETTINGS = 14;
  // The enter amount screen in Payment flow
  PAY_ENTER_AMOUNT = 15;
  // Category Analyser screen
  CATEGORY_ANALYSER = 16;
  // Debit card limits home fragment
  DEBIT_CARD_LANDING = 17;
  // Screen for viewing debit card limits
  DEBIT_CARD_LIMITS_HOME = 18;
  // Screen for updating Debit card limits
  DEBIT_CARD_UPDATE_LIMIT = 19;
  // Screen for scanning QR code for new card request
  DEBIT_CARD_ACTIVATE_QR_CODE = 20;
  // Screen for filling reasons for new Debit card request
  DEBIT_CARD_REQUEST_CUSTOM_REASONS = 21;
  // Screen for starting New card request flow
  DEBIT_CARD_NEW_CARD_REQUEST = 22;
  // Screen showing the list of Debit card request reasons
  DEBIT_CARD_REQUEST_REASONS_LIST = 23;
  // Success screen for new Debit card request
  DEBIT_CARD_REQUEST_SUCCESS = 24;
  // Debit card offers home/landing screen
  DEBIT_CARD_OFFERS_HOME = 25;
  // Debit card offer details screen
  DEBIT_CARD_OFFER_DETAILS = 26;
  // Debit card settings screen
  DEBIT_CARD_SETTINGS = 27;
  // Debit card ATM pin setting screen
  DEBIT_CARD_ATM_PIN_OPTIONS = 28;
  // Debit card ATM pin reset screen
  DEBIT_CARD_RESET_ATM_PIN_OPTIONS = 29;
  // Save/Deposit module's template list screen
  DEPOSITS_TEMPLATES_LIST = 30;
  // Add money screen shown during Deposit creation/SD add more funds
  DEPOSIT_ADD_MONEY = 31;
  // Add name screen displayed during Smart/Fixed deposit creation
  DEPOSIT_ADD_NAME = 32;
  // Dialog shown when starting Smart/Fixed deposit pre-closure, with information about benefits of retaining the
  // deposit
  DEPOSIT_CLOSURE_NUDGES = 33;
  // 2nd dialog shown when user continues pre-closing the Smart/Fixed deposit
  DEPOSIT_CLOSURE_CONFIRMATION = 34;
  // The Interest payout frequency screen shown during Fixed deposit creation
  DEPOSIT_PAYOUT_MODE_SELECTION = 35;
  // The payment selection screen shown during Deposit creation
  DEPOSIT_SELECT_PAYMENT_METHOD = 36;
  // Fittt module collection information screen
  FITTT_COLLECTION_INFO = 37;
  // Fittt screen for exploring all collections
  FITTT_EXPLORE_COLLECTIONS = 38;
  // Fittt module introduction screen with carousel
  FITTT_INTRO_SCREEN = 39;
  // Fittt screen for user's active subscriptions
  FITTT_ALL_SUBSCRIPTIONS = 40;
  // Fittt screen for user's rules
  FITTT_EXPLORE_MY_RULES = 41;
  // Fittt screen for previewing rule subscription
  FITTT_RULE_SUBSCRIPTION_PREVIEW = 42;
  // View all fittt rules
  FITTT_ALL_RULES = 43;
  // Fittt Sports rules intro screen
  FITTT_SPORTS_INTRO = 44;
  // Fittt Sports rule landing screen
  FITTT_SPORTS_CHALLENGE_LANDING = 45;
  // Fittt Sports rule reward screen
  FITTT_SPORTS_CHALLENGE_REWARD = 46;
  // Match details screen for Fittt sport rules
  FITTT_SPORTS_CHALLENGE_MATCH_DETAILS = 47;
  // Fittt rule execution history
  FITTT_RULE_EXECUTION_HISTORY = 48;
  // Fittt rule subscription details
  FITTT_SUBSCRIPTION_DETAILS = 49;
  // Help module Article detail screen
  HELP_ARTICLE = 50;
  // Help category screen
  HELP_CATEGORY = 51;
  // Help Stories topic screen
  HELP_STORIES_TOPIC = 52;
  // Account details screen for Smart/Fixed deposits
  DEPOSIT_ACCOUNT_DETAILS = 53;
  // In-app notifications center screen
  NOTIFICATION_CENTER = 54;
  // Rewards promotion banner screen
  REWARDS_PROMO_BANNER_SCREEN = 55;
  // First introduction screen in Onboarding flow
  ONBOARDING_INTRO_SCREEN = 56;
  // Screen for starting fund transfer when closing min-kyc account
  ONBOARDING_ACCOUNT_CLOSURE_TRANSFER_INIT = 57;
  // Onboarding add funds screen
  ONBOARDING_ADD_MONEY = 58;
  // Onboarding address confirmation screen
  ONBOARDING_CONFIRM_ADDRESS = 59;
  // Onboarding App features list screen
  ONBOARDING_APP_FEATURES_SCREEN = 60;
  // Onboarding duplicate account (de-dupe check) screen
  ONBOARDING_ACCOUNT_DUPLICATE = 61;
  // Screen for showing detailed information in onboarding flow
  ONBOARDING_INFO_ACK = 62;
  // Screen for adding Parent's name and Nominee selection in Onboarding
  ONBOARDING_KYC_PARENT_NAME = 63;
  // Screen for Email account selection and verification, in onboarding
  ONBOARDING_EMAIL_ACCOUNT_VERIFICATION = 64;
  // Screen for showing 'All set' state during onboarding account creation flow
  ONBOARDING_ALL_SET = 65;
  // Screen for entering Finite code in onboarding
  ONBOARDING_ENTER_FINITE_CODE = 66;
  // Screen for entering Pan number and Date of birth
  ONBOARDING_KYC_ENTER_PAN_DOB = 67;
  // Screen for getting user consent in Onboarding flow
  ONBOARDING_TNC_SCREEN = 68;
  // Screen for getting consent so that whatsapp communication can be sent to users
  ONBOARDING_WHATSAPP_CONSENT_SCREEN = 69;
  // Screen showing the 'Open Account' Cta in onboarding flow
  ONBOARDING_CREATE_ACCOUNT_CONSENT = 70;
  // Screen for setting debit card pin in onboarding flow
  ONBOARDING_DEBIT_CARD_SET_PIN = 71;
  // Screen for collecting User consent for credit report check
  ONBOARDING_SCREENER_CREDIT_REPORT_CONSENT = 72;
  // Screen for collecting Employment details during onboarding screener flow
  ONBOARDING_SCREENER_EMPLOYMENT_DECLARATION = 73;
  // Screen for showing Experian T&C
  ONBOARDING_SCREENER_EXPERIAN_TNC = 74;
  // Screen for when users employment declaration verification is not successful and the have credit report available.
  // Users need to provide consent to check their Credit Report
  ONBOARDING_SCREENER_MANDATE_CREDIT_REPORT_CONSENT = 75;
  // Screen to allow user to verify their work status by giving us permission to read their gmail. We can verify it
  // based on their transactions
  ONBOARDING_SCREENER_VERIFY_WITH_GMAIL = 76;
  // Screen is an upcoming stage in work status verification. But we need to show user that this is an alternative to
  // gmail-verification which they can try later. This screen & deeplink will be deprecated once we have linkedin
  // verification implemented
  ONBOARDING_SCREENER_LINKEDIN_VERIFICATION = 77;
  // Screen to allow user to enter their work-email to verify their work status
  ONBOARDING_SCREENER_ENTER_WORK_EMAIL = 78;
  // Screen to allow user to enter & verify the otp sent to their work email
  ONBOARDING_SCREENER_WORK_EMAIL_OTP = 79;
  // Screen for showing All transactions for various accounts of a user
  PAY_ALL_TRANSACTIONS = 80;
  // Screen to allow users to search for transactions
  PAY_SEARCH_TRANSACTIONS = 81;
  // Info screen before creating, authorizing or executing a recurring payment
  PAY_AUTO_PAY_AMOUNT_SCREEN = 82;
  // Screen used for creating / editing recurring payment rule. Ideally this should be happening through Fittt. So, for
  // now we have copied components from Fittt and modified it as per requirements
  PAY_CREATE_RECURRING_PAYMENT = 83;
  // Screen to show details for a recurring payment
  PAY_RECURRING_PAY_DETAILS = 84;
  // Screen to show list of activities for a recurring payment
  PAY_RECURRING_PAY_EXECUTION_INFO = 85;
  // Screen to list all recurring payment based on status
  PAY_FILTER_RECURRING_PAYMENTS = 86;
  // Home/landing page for auto pay
  PAY_AUTO_PAY_HOME = 87;
  // Screen to show intro for auto-pay
  PAY_AUTO_PAY_INTRO = 88;
  // Bank transfer form screen in Pay module
  PAY_BANK_TRANSFER = 89;
  // This screen collects inputs from user for the dispute & allows them to raise a dispute or ask for help
  PAY_DISPUTE_INPUT = 90;
  // Entry point for all intent based payment received by the app
  PAY_INTENT_SCREEN = 91;
  // Screen for for Payment via Phone number
  PAY_PHONE_PAYMENT = 92;
  // Scan QR code for UPI payment
  PAY_QR_SCAN = 93;
  // Pay search screen for searching upi ids/users
  PAY_SEARCH = 94;
  // Screen to set/change NPCI PIN
  PAY_UPDATE_NPCI_PIN = 95;
  // Pay timeline screen for an entity (merchant/user)
  PAY_TIMELINE = 96;
  // Shows lists of categories to select from for a transaction
  PAY_TRANSACTION_CATEGORY = 97;
  // Screen to show to categorise multiple transactions
  PAY_TRANSACTION_CATEGORY_SIMILAR_TXN = 98;
  // Transaction receipt screen for a payment
  PAY_TRANSACTION_RECEIPT = 99;
  // Screen responsible for collecting and validating the UPI id
  PAY_UPI_PAYMENT = 100;
  // Screen in user's profile module, to show list of user's accounts
  PROFILE_ACCOUNTS_LIST = 101;
  // Account settings screen in User profile
  PROFILE_ACCOUNT_SETTINGS = 102;
  // Settings screen for a user's connected account
  PROFILE_CONNECTED_ACCOUNT_SETTINGS = 103;
  // Privacy and security screen in Profile
  PROFILE_PRIVACY_AND_SECURITY = 104;
  // Screen in User Profile to show user's QR code
  PROFILE_SHOW_QR = 105;
  // Screen to show legal terms in User profile
  PROFILE_SETTINGS_LEGAL = 106;
  // Dialog to show Logout CTA
  PROFILE_APP_LOGOUT_DIALOG = 107;
  // Profile Notification settings screen
  PROFILE_NOTIFICATION_SETTINGS = 108;
  // Edit profile screen in User profile section
  PROFILE_EDIT = 109;
  // Screen to view user's personal details
  PROFILE_PERSONAL_DETAILS = 110;
  // Screen to show user's referral history
  REFERRALS_HISTORY = 111;
  // Screen to show Users referral code, invited users etc.
  REFERRALS_INVITE_FRIENDS = 112;
  // Screen to show the qualifying action details and Cta for referrals
  REFERRALS_QUALIFYING_ACTION_REQUIRED = 113;
  // Referral terms screen
  REFERRALS_TNC = 114;
  // Screen to show maximum referral rewards reached
  REFERRALS_REWARD_CAP_REACHED = 115;
  // Screen for resetting referral rewards cap
  REFERRALS_REWARD_RESET_CAP = 116;
  // Reward claim screen
  REWARDS_CLAIM = 117;
  // Reward details screen
  REWARDS_DETAIL = 118;
  // Rewards ways to earn screen
  REWARDS_WAYS_TO_EARN = 119;
  // Screen to show information about Rewards
  REWARDS_INFO = 120;
  // Landing/home screen for rewards
  REWARDS_LANDING = 121;
  // Screen for showing collected offers
  OFFERS_COLLECTED = 122;
  // Screen for showing details about an offer
  OFFERS_DETAILS = 123;
  // Screen to show details of redeemed offer
  OFFERS_EXCHANGED_OFFER_DETAILS = 124;
  // Screen to show successful redemption of Gift voucher
  OFFERS_REDEEMED_GIFT_VOUCHER = 125;
  // Screen to show details of redeemed Fi coins
  OFFERS_COIN_EXCHANGE_CLAIM = 126;
  // Screen for showing details of exchange reward once it is successful
  OFFERS_COIN_EXCHANGE_SUCCESS = 127;
  // Offers landing/home screen
  OFFERS_LANDING = 128;
  // Offers terms and conditions screen
  OFFERS_DETAIL_TNC = 129;
  // Screen for editing a Reward Smart deposit
  REWARDS_SD = 130;
  // Salary introduction screen
  SALARY_INTRO = 131;
  // Salary module landing/home screen
  SALARY_LANDING = 132;
  // Screen for showing all benefits on salary program
  SALARY_ALL_BENEFITS = 133;
  // Screen to show all available active benefits
  SALARY_ACTIVE_BENEFITS = 134;
  // Askfi/Search landing screen
  ASKFI_LANDING = 135;
  // Poginated Screen for showing all financial activities from an ask-fi results screen
  ASKFI_ALL_FINANCIAL_ACTIVITY = 136;
  // Askfi suggestions screen
  ASKFI_SUGGESTIONS = 137;
  // Screen for showing Vkyc related information
  VKYC_INFO = 138;
  // Vkyc landing/home screen
  VKYC_LANDING = 139;
  // Screen to display information about a scheduled Vkyc call
  VKYC_SCHEDULED = 140;
  // Screen to display vkyc incompatible info and related actions
  VKYC_DEVICE_INCOMPATIBLE = 141;
  // Screen to show pending status of Vkyc
  VKYC_STATUS = 142;
  // Screen for Adhaar e-sign missing data submitted by user to be updated on KRA
  WEALTH_ADHAAR_ESIGN = 143;
  // Screen to capture image of PAN card from the user
  WEALTH_COLLECT_PAN = 144;
  // Screen to collects user's signature
  WEALTH_COLLECT_SIGNATURE = 145;
  // Screen to collect acknowledgement/consent from User for updating their KYC data. A SEBI mandatory screen
  WEALTH_CONSENT = 146;
  // Screen to show landing screen for Digilocker flow
  WEALTH_DIGILOCKER_LANDING = 147;
  // Screen to show Digilocker using webview
  WEALTH_DIGILOCKER_WEBVIEW = 148;
  // Screen used for fund selection in one time investment
  WEALTH_OTI_FUND_SELECTION = 149;
  // Landing screen for Wealth Onboarding
  WEALTH_ONBOARDING_LANDING = 150;
  // Displays monetary activities against a mutual fund for an user
  WEALTH_MF_ACTIVITY = 151;
  // Screen to list down the options available to invest in Mutual funds
  WEALTH_MF_LIST = 152;
  // Screen to display all mutual funds available against a collection
  WEALTH_MF_ALL_COLLECTIONS = 153;
  // Landing screen for Mutual Fund collections. This screen custom MF collections to users and allows the capability
  // to search, sort, bookmark, etc
  WEALTH_MF_COLLECTIONS_LANDING = 154;
  // Screen to display various details about a Mutual fund
  WEALTH_MF_DETAILS = 155;
  // Screen to show a user's investment details
  WEALTH_MF_INVESTED_DETAILS = 156;
  // Landing screen for p2p investments . We can set the amount of investment to be done. Can directly land here from
  // either dashboard or soon after eligibility checks are done and you are cleared to invest
  WEALTH_P2P_LANDING = 157;
  // Screen to show complete details for a Mutual fund/Investment order
  WEALTH_ORDER_DETAILS = 158;
  // This screen handles multiple scenarios based on WealthOnboardingAction status
  WEALTH_ONBOARDING_STATUS = 159;
  // Screen to record missing KYC information in BE during Wealth on-boarding
  WEALTH_ONBOARDING_MISSING_KYC_DATA = 160;
  // Generic screen to display title, description and a deeplink driven CTA
  WEALTH_ONBOARDING_MISSING_KYC_INTRO = 161;
  // Screen to record user consent before performing Liveness video verification
  LIVENESS_CONSENT = 162;
  // Screen to record user's video for liveness verification
  LIVENESS_RECORDING = 163;
  // Fragment which shows benefits of connected accounts. The actual purpose of the screen is to act like an dummy entry
  // point to connected accounts. clicking on connected accounts entry point (i.e: Notification or home banner) will be
  // taken to this screen and not to the OTP immediately.
  AA_BENEFITS = 164;
  // Screen to check with backend if the user is wealth onboarded or not. Based on backend provided deeplink start
  // various flow
  AA_ACCOUNT_INIT = 165;
  // Information screen for the user before going into the KYC
  ONBOARDING_EKYC_CONSENT = 166;
  // Screen to retry EKYC verification after Name Mismatch
  ONBOARDING_EKYC_MISMATCH = 167;
  // Screen for customising fit rules
  FITTT_RULE_CUSTOMISATION = 168;
  // Screen for tweaking various usage rules/restrictions for Debit card
  DEBIT_CARD_USAGE_SETTING = 169;
  // Screen for tracking Debit card dispatch
  DEBIT_CARD_TRACKING = 170;
  // Mutual funds landing/digest tab
  HOME_MUTUAL_FUNDS_LANDING_TAB = 171;
  // P2P Consent screen
  P2P_LANDING_CONSENT = 172;
  // P2P Activity details fragment. Is showed when user clicks on any activity in all Activity Screen
  P2P_ACTIVITY_DETAILS = 173;
  // P2P All Activities fragment. Is showed when user clicks on All Activity CTA on dashboard screen
  P2P_ALL_ACTIVITIES = 174;
  // P2P Eligibility check screen in Jump tab
  P2P_ELIGIBILITY_CHECK = 175;
  // Know more screen for P2P investments
  P2P_KNOW_MORE = 176;
  // Onboarding Account dededupe error screen
  ONBOARDING_DEDUPE_ERROR = 177;
  // Screen for showing detailed error status for onboarding
  ONBOARDING_DETAILED_ERROR = 178;
  // Screen for entering/selecting user phone number and OTP verification
  ONBOARDING_PHONE_VERIFICATION = 180;
  // Screen for taking user consent for Safetynet verification
  ONBOARDING_SAFETY_NET_CONSENT = 181;
  // Screen for showing screener state in Manual intervention for user
  SCREENER_MANUAL_INTERVENTION = 182;
  // Shows the detail of already exchanged coins to money
  OFFERS_EXCHANGE_COIN_DETAIL = 183;
  // Details the ways to earn a active reward
  REWARDS_WAYS_TO_EARN_DETAIL = 184;
  // Selection screen for selecting a salary transaction
  SALARY_TRANSACTION_SELECTOR = 185;
  // Fragment is shown when E-KYC limit in days has expired. User needs to complete E-KYC before moving on V-KYC
  VKYC_EKYC_EXPIRED = 186;
  // Showing Vkyc status post onboarding for user is complete
  VKYC_STATUS_POST_ONBOARDING = 187;
  // Displays Selection based choice to users
  WEALTH_COLLECT_SINGLE_CHOICE = 188;
  // Error handling for Wealth on-boarding
  WEALTH_ONBOARDING_ERROR = 189;
  // The P2P/Jump Landing screen
  P2P_LANDING_TAB = 190;
  // used when we want to show instructions before continuing to do an action
  ONBOARDING_INSTRUCTIONS = 191;
  // The ML-kit based Pay QR Scan screen
  PAY_QR_SCAN_ML_KIT = 192;
  // Entry point via Deeplink urls (Like marketing deeplink urls which users can click and deeplink into the app)
  DEEPLINK_URL = 193;
  // Entry point via the mobile device's system tray notifications
  SYSTEM_TRAY_NOTIFICATION = 194;
  // Entry point for editing user profile info
  EDIT_PROFILE_INFO = 195;
  // Screen to show all quick links on salary feature
  SALARY_ALL_QUICK_LINKS = 196;
  // Insights hub screen in Analyser module
  INSIGHTS_HUB = 197;
  // intro screen to connect accounts for screener.
  SCREENER_CONNECTED_ACCOUNTS_INFO = 198;
  // Tier introduction screen
  TIER_INTRODUCTION_SCREEN = 199;
  // Tier overview screen
  TIER_OVERVIEW_SCREEN = 200;
  // Tier upgrade success screen
  TIER_UPGRADE_SUCCESS_SCREEN = 201;

  // New Home landing screen
  HOME_LANDING_V2 = 202;

  // Screen name for the Home V2 Explore Tab Screen
  HOME_EXPLORE_SCREEN = 203;
  // Screen name for new invest landing screen
  INVEST_LANDING = 204;

  // The US stocks tab shown in Home Pull Down UI
  HOME_US_STOCKS_TAB = 205;
  // US stocks landing page explore tab
  US_STOCKS_LANDING_EXPLORE = 206;
  // US stocks landing page portfolio tab / your stocks
  US_STOCKS_LANDING_YOUR_STOCKS = 207;
  // US stocks landing page watchlist tab
  US_STOCKS_LANDING_WATCHLIST = 208;
  // US stocks collections screen
  US_STOCKS_COLLECTION = 209;
  // US stocks symbol details screen
  US_STOCKS_DETAILS = 210;
  // US stocks search screen
  US_STOCKS_SEARCH = 211;
  // Screen for jump available plans screen
  P2P_INVESTMENT_AVAILABLE_PLANS_INFO = 381;
  // Screen options for jump choose plan screen
  P2P_INVESTMENT_CHOOSE_PLAN = 382;
  // Screen options for jump unlock plan screen
  P2P_INVESTMENT_UNLOCK_PLAN = 383;
  // Screen to select or enter amount to withdraw
  // Client is supposed to call P2PInvestments's 'GetDeeplink' rpc for rendering this
  P2P_WITHDRAW_MONEY_ENTER_AMOUNT = 385;
  // Screen to show withdrawal amount break across different scheme
  // Client is supposed to call P2PInvestment's 'GetWithdrawMoneyAttributes' rpc for rendering this
  P2P_WITHDRAW_MONEY_SUMMARY = 386;
  P2P_INVESTMENT_VIEW_BREAKUP_SCREEN = 409;
  P2P_INVESTMENT_OTP_SCREEN = 460;
  P2P_CONFIRM_INVESTMENT_RENEWAL_SCREEN = 461;
  P2P_INVESTMENT_CURRENT_STATUS_SCREEN = 464;
  P2P_INVESTMENT_ACTIVITY_DETAILS_SCREEN = 465;
  P2P_INVESTMENT_ALL_UPCOMING_RENEWALS_SCREEN = 466;
  P2P_INVESTMENT_RENEWAL_CANCELLATION_NUDGE_SCREEN = 467;
  US_STOCK_BUY_ENTER_AMOUNT = 468;
  US_STOCK_BUY_SUMMARY = 469;
  US_STOCK_SELL_ENTER_AMOUNT = 470;
  US_STOCK_SELL_SUMMARY = 471;
  US_STOCK_ORDER_DETAILS = 472;
  US_STOCK_ONB_STMNTS_PAN_FETCH = 473;
  US_STOCK_ONB_DECLARATION = 474;
  US_STOCK_ONB_DISCLAIMER = 475;
  US_STOCK_ONB_EMPLOYER_DETAILS = 476;
  US_STOCK_ONB_INVESTMENT_RANGE = 478;
  US_STOCK_ONB_PAN_UPLOAD_OPTIONS = 479;
  US_STOCK_ONB_PAN__UPLOAD_CAMERA = 480;
  US_STOCK_ONB_PAN__UPLOAD_GALLERY = 481;
  US_STOCK_EXISTING_CONNECTED_ACCOUNT = 482;
  US_STOCK_NEW_CONNECTED_ACCOUNT = 483;
  US_STOCK_SOF_POLLING = 484;
  US_STOCK_ONB_FUTURE_SCOPE = 485;
  US_STOCK_SOF_FUTURE_SCOPE = 486;
  US_STOCK_SET_UP_ACCOUNT_DIALOG = 487;
  US_STOCK_SET_UP_SOF_DIALOG = 488;
  US_STOCK_PREREQUISITE_DIALOG = 489;

  // Investment risk profile questionnaire screen
  INVESTMENT_RISK_PROFILE_QUESTIONNAIRE = 490;
  // Investment risk profile dashboard screen
  INVESTMENT_RISK_PROFILE_DASHBOARD = 491;
  VKYC_INTRO = 492;
  VKYC_PAN_TYPE_SELECTION = 493;
  VKYC_INSTRUCTIONS = 494;
  US_STOCKS_SEARCH_LANDING = 495;
  US_STOCK_LANDING_PAGE = 496;
  US_STOCKS_SEARCH_RESULT = 497;
  //Reminder
  REMINDERS_ENTRY = 498;
  REMINDERS_LANDING = 499;
  REMINDERS_SET = 500;

  // auto save suggestions screen in the deposit creation flow
  DEPOSIT_AUTO_SAVE_SUGGESTIONS = 501;
  VKYC_ERROR_SCREEN = 502;

  //Reminder
  REMINDER_OVER_SPEND = 503;
  REMINDER_DATE_CHANGE_PAGE = 504;
  REMINDER_NO_CC_PAGE = 505;

  //CX Language Selection
  CX_LANGUAGE_SELECTION_SCREEN = 506;
  CX_LANGUAGE_SELECTION_SUCCESS_SCREEN = 507;
  CX_LANGUAGE_OPTION_SUGGESTION_SCREEN = 508;

  //Screen display individual generic screen - https://www.figma.com/file/WGeiokaZndC3vfTbwJ6WJh/Help-%E2%80%A2-FFF-%E2%80%A2-v1.0?type=design&node-id=3639%3A7348&t=GGXxlsKfeHT05jyM-1
  STORY_SCREEN = 509;

  //CX Ticket Screens
  CX_TICKET_DETAIL_SCREEN = 510;
  CX_TICKET_LIST_SCREEN = 511;
  CX_TICKET_TAB_SCREEN = 512;

  //CX Chat
  CX_CHAT_SCREEN = 513;
  // App Shortcuts entry point identifier. These entry points are surfaced, when user long-presses on the Launcher icon
  // in iOS/Android
  APP_SHORTCUTS = 514;

  // Disconnect account
  DISCONNECT_ACCOUNT_CONFIRM_VIEW_CONTROLLER = 515;
  DISCONNECT_ACCOUNT_BOTTOM_SHEET_VIEW_CONTROLLER = 516;

  // Connected Account: connecting Fi to Fi flow to connect Fi Federal Savings Account
  CA_LANDING_PAGE_FI_TO_FI_FLOW_SCREEN = 517;
  CA_INIT_SDK_FI_TO_FI_FLOW_SCREEN = 518;

  DEBIT_CARD_CONSOLIDATED_USAGE_SETTING = 519;
  // DEBIT CARD ACTIVATION SCREENS
  // Screen that initiates the debit card activation flow via the secure pin . This screen will be enabled for the user 5-7 days post
  // the card receipt for the user.
  DEBIT_CARD_PIN_ACTIVATION_SCREEN = 621;
  // screen to be displayed when the card activation has been done successfully
  DEBIT_CARD_SUCCESSFUL_ACTIVATION_SCREEN = 622;
  // screen which will contain the qr scanner used to scan the qr code for card activation
  DEBIT_CARD_QR_CODE_SCAN_SCREEN = 623;
  // Screen to show processing state in payment flow
  PAY_PROCESSING = 624;
  // Screen to show status of payment in payment flow
  POST_PAYMENT = 625;

  // US Stocks onboarding Success Screen.
  US_STOCKS_ONB_SUCCESS = 626;
  // US STOCKS Onboarding Polling. This screen is a polling screen to check for completion of US STOCKS account creation
  US_STOCKS_ONB_POLLING = 627;
  // US STOCKS Onboarding Address ID Proof Screen to check for ID Proofs of Client
  US_STOCKS_ONB_ADDRESS_ID_PROOF = 628;
  // US STOCKS Onboarding Error Screen to show errors on any screen during Onboarding of US STOCKS
  US_STOCKS_ONB_ERROR = 629;
  // US STOCKS PN Onboarding landing screen
  US_STOCKS_PN_ONB_LANDING = 630;
  // Screen to update Maturity options for a Jump Investment.
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?type=design&node-id=15353-23902&t=akSt8sz2mwKyN7c5-4
  P2P_UPDATE_MATURITY_CONSENT_DIALOG = 631;
  // https://drive.google.com/file/d/12p_o9n4Gsv-1y2tYv3XRw1721j1qSRVA/view?usp=drive_link
  DEPOSIT_ADD_MONEY_FREQUENCY = 632;
  // Processing screen post user Swipes to Close Deposit.
  DEPOSIT_CLOSURE_PROCESSING = 633;
  // https://drive.google.com/file/d/19eN7NsgN12mZE_BJ3zGeXFI7zK_oHATS/view?usp=drive_link
  DEPOSIT_CREATION_PROCESSING = 634;
  // https://drive.google.com/file/d/17SAdlW6iBMslGP-CSggft4nMKJsZNfAa/view?usp=drive_link
  // https://drive.google.com/file/d/16JFMxfv9emcS_8RCDiKsMey_tjdcfqBg/view?usp=drive_link
  DEPOSIT_CREATION_AUTO_SAVE_PROCESSING = 635;
  //https://drive.google.com/file/d/1-ISBOobbiVpcGn4OKPiSH7T1Jh4Z7T6q/view?usp=drive_link
  // https://drive.google.com/file/d/1BY7M3E9qAN9zuF52bF6ajjsNh5DTJwf5/view?usp=drive_link
  DEPOSIT_GENERATE_STATEMENT = 636;
  // https://drive.google.com/file/d/1vS8hn8VQa16-YOhCnd88xQeT4ZS6D-PO/view?usp=drive_link
  DEPOSIT_TERM_SELECTION = 637;
  // https://drive.google.com/file/d/1jz2MtzfULImAO7rk11fSYgzBlvIyC7vz/view?usp=drive_link
  DEPOSIT_EXPLAINER_DIALOG = 638;
  // https://drive.google.com/file/d/1rCrAZHTAlq5pIkd2C--WHJmsMo2wLN2J/view?usp=drive_link
  DEPOSIT_EXPLAINER_COMPARISON_DIALOG = 639;
  // Onboarding screener choice screen where user can select any of the way to verify the screener
  ONBOARDING_SCREENER_CHOICE = 640;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=28184-82719&mode=design&t=BhOlaRTpDBigUS4k-4
  CC_INTRO_SCREEN = 641;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=25344-92911&mode=design&t=BhOlaRTpDBigUS4k-4
  CC_ADDRESS_SELECTION_SCREEN = 642;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8569-36634&mode=design&t=BhOlaRTpDBigUS4k-4
  CC_BILL_GENERATION_SCREEN = 643;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=6504-38493&mode=design&t=zBKNYfLWUHEEAal7-4
  CC_LIVENESS_AND_FACE_MATCH_SCREEN = 644;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=6530-51906&mode=design&t=zBKNYfLWUHEEAal7-4
  CC_VKYC_FLOW_SCREEN = 645;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=7194-24587&mode=design&t=k9lF6Nu2YHsYkySs-4
  CC_CARD_CREATION_SCREEN = 646;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=23700-89970&mode=design&t=k9lF6Nu2YHsYkySs-4
  CC_WELCOME_OFFER_SELECTION_SCREEN = 647;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=28184-82960&mode=design&t=vGdJxXI49Crq7hyy-4
  CC_VALUE_BACK_SCREEN = 648;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=30903-98447&mode=design&t=HOtNGzh3XtqXOPLj-4
  CC_ACCELERATED_REWARDS_SCREEN = 649;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=28184-83636&mode=design&t=vGdJxXI49Crq7hyy-4
  CC_WELCOME_VOUCHERS_SCREEN = 650;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=23643-103872&mode=design&t=WYx3spVKzSOm3Eqd-4
  CC_WELCOME_OFFER_SELECTION_SUCCESS_SCREEN = 651;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=10257-47824&mode=design&t=WYx3spVKzSOm3Eqd-4
  CC_LANDING_SCREEN = 652;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=10436-44110&mode=design&t=PDmaDlD6I2mkHxRN-4
  CC_CONTROLS = 653;

  // Early Salary screen name
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20106-39249&mode=design&t=cIh1uXQposnj2v0T-4
  ES_ELIGIBILITY_LANDING = 654;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20167-39908&mode=design&t=cIh1uXQposnj2v0T-4
  ES_APPLICATION_LANDING = 655;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=24796-44371&mode=design&t=cIh1uXQposnj2v0T-4
  ES_CUSTOM_OFFER_SELECTION_DIALOG = 656;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46984&mode=design&t=cIh1uXQposnj2v0T-4
  ES_APPLICATION_DETAILS = 657;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=21791-44695&mode=design&t=cIh1uXQposnj2v0T-4
  ES_AUTO_REPAYMENT = 658;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46899&mode=design&t=cIh1uXQposnj2v0T-4
  ES_ADDRESS_CONFIRMATION = 659;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46585&mode=design&t=cIh1uXQposnj2v0T-4
  ES_INITIATE_ESIGN = 660;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46605&mode=design&t=cIh1uXQposnj2v0T-4
  ES_ESIGN_VIEW_DOCUMENT = 661;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46638&mode=design&t=cIh1uXQposnj2v0T-4
  ES_APPLICATION_STATUS = 662;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20182-48203&mode=design&t=cIh1uXQposnj2v0T-4
  ES_DASHBOARD = 663;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46255&mode=design&t=cIh1uXQposnj2v0T-4
  ES_LOAN_DETAILS = 664;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=20176-46765&mode=design&t=cIh1uXQposnj2v0T-4
  ES_APPLICATION_STATUS_POLL = 665;

  // Personal Loans Screen name
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-19636&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_LANDING = 666;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20843&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_OFFER_DETAILS = 667;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-19985&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_APPLICATION_DETAILS = 668;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-19636&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_NOT_QUALIFIED_USERS = 669;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=18721-35584&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_INITIATE_MANDATE = 670;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20349&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_INITIATE_ESIGN = 671;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20412&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_ESIGN_VIEW_DOCUMENT = 672;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20455&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_APPLICATION_CONFIRMATION_VIA_OTP = 673;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20204&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_ADDRESS_CONFIRMATION = 674;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=16440-32289&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_EMPLOYMENT_DETAILS = 675;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=16051-31940&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_POLLING_SCREEN = 676;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20439&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_APPLICATION_STATUS = 677;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-21079&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_LOAN_DASHBOARD = 678;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=3526-13511&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_PRE_PAY = 679;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=9783-10358&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_ACTIVITY_STATUS = 680;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=21974-44764&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_DOWNTIME = 681;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=19433-37057&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_ENTER_DETAILS = 682;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15220-20509&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_LOAN_DETAILS = 683;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=19433-37194&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_REVIEW_DETAILS = 684;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=19433-37255&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_OCCUPATION_DETAILS = 685;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=15453-29350&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_UPDATED_RATE = 686;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=5263-15963&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_TRANSACTION_RECEIPT = 687;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=5528-24094&mode=design&t=FGfaipheeFQ0XgCg-4
  PL_ALL_TRANSACTIONS = 688;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=9480-41391&mode=design&t=yZizPhbECUhGjE7k-4
  CC_USAGE_SCREEN = 689;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=9468-42159&mode=design&t=yZizPhbECUhGjE7k-4
  CC_LIMITS_SCREEN = 690;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15217-65138&mode=design&t=K09kUhQFxea4ekHF-4
  CC_ALL_TRANSACTIONS_SCREEN = 691;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=23004-84719&mode=design&t=K09kUhQFxea4ekHF-4
  CC_TRANSACTION_RECEIPT_SCREEN = 692;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11436-46606&mode=design&t=K09kUhQFxea4ekHF-4
  CC_BILL_REPAYMENT_SELECTION_SCREEN = 693;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11436-56480&mode=design&t=K09kUhQFxea4ekHF-4
  CC_CUSTOM_AMOUNT_BILL_PAY_SCREEN = 694;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11361-49404&mode=design&t=K09kUhQFxea4ekHF-4
  CC_PAYMENT_STATUS_SCREEN = 695;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=6093-28009&mode=design&t=K09kUhQFxea4ekHF-4
  CC_SET_CARD_PREFERENCES_SCREEN = 696;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8921-37964&mode=design&t=K09kUhQFxea4ekHF-4
  CC_SET_CARD_PIN = 697;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8914-36659&mode=design&t=K09kUhQFxea4ekHF-4
  CC_PHYSICAL_CARD_TRACKING_SCREEN = 698;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=14298-60202&mode=design&t=K09kUhQFxea4ekHF-4
  CC_VIEW_STATEMENT_SCREEN = 699;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=14298-61459&mode=design&t=K09kUhQFxea4ekHF-4
  CC_EXPORT_STATEMENT_SCREEN = 700;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=10401-42747&mode=design&t=K09kUhQFxea4ekHF-4
  CC_FREEZE_UNFREEZE_SCREEN = 701;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=10206-42332&mode=design&t=K09kUhQFxea4ekHF-4
  CC_NEW_CARD_REQUEST_ADDRESS_SELECTION_SCREEN = 702;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8977-38866&mode=design&t=K09kUhQFxea4ekHF-4
  CC_NEW_CARD_REQUEST = 703;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17696-72536&mode=design&t=K09kUhQFxea4ekHF-4
  CC_EMI_DASH_BOARD_SCREEN = 704;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17503-73225&mode=design&t=K09kUhQFxea4ekHF-4
  CC_VIEW_ALL_EMI_TRANSACTIONS_SCREEN = 705;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=16896-72866&mode=design&t=K09kUhQFxea4ekHF-4
  CC_EMI_TRANSACTION_LOAN_OFFERS_SCREEN = 706;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15971-68591&mode=design&t=K09kUhQFxea4ekHF-4
  CC_EMI_PREVIEW_PRE_CLOSE_LOAN_SCREEN = 707;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15576-66172&mode=design&t=K09kUhQFxea4ekHF-4
  CC_EMI_LOAN_ACCOUNT_DETAILS_SCREEN = 708;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15993-67391&mode=design&t=K09kUhQFxea4ekHF-4
  CC_EMI_PRE_CLOSE_LOAN_SCREEN = 709;
  //https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=1-4040&mode=design&t=LpJ2VJ0A9BM8056O-4
  CC_SECURED_OPEN_FD_SCREEN = 710;
  //https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=779-17822&mode=design&t=LpJ2VJ0A9BM8056O-4
  CC_SECURED_FD_DETAILS_SCREEN = 711;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=27648-82126&mode=design&t=Z1NgpXUrnD4fydIF-4
  CC_REAL_TIME_ELIGIBILITY_CHECK_INTRO_SCREEN = 712;
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=12532-44765&mode=dev
  DEPOSIT_CREATION_SCREEN = 713;
  // This screen is used for mentioning the screen name in the events for the clicking of the tabs
  // Debit card and credit card tab screen
  CARD_TABS_SCREEN = 714;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=31062-31806&mode=design&t=RDFo7azNWxu6eAL0-4
  CC_BILL_GENERATION_DATE_SELECTION_SCREEN = 715;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=9468-42546&mode=design&t=HPH0hT8EGJF2mHma-4
  CC_EDIT_LIMITS_SCREEN = 716;
  //https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=1-4040&mode=design&t=LpJ2VJ0A9BM8056O-4
  CC_SECURED_TENURE_SELECTION_BOTTOM_SHEET_SCREEN = 717;
  //https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=84-16721&mode=design&t=dqR9qTEzJXlhyEtX-4
  CC_SECURED_FD_INFO_SCREEN = 718;
  // https://drive.google.com/file/d/1KQI6qM3FMttsoIrHpPsRF49F7CqTw8I0/view?usp=drive_link
  DEPOSIT_DECLARATION = 719;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=5582%3A176745&t=pmOfrvWkNVTHSL94-0
  CC_AUTH_OPTIONS_SCREEN = 720;
  //  https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?type=design&node-id=23643-102517
  CC_BENEFITS_SCREEN = 721;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8921%3A37964&t=poCwdMZL3AsSfgjE-0
  CC_CUG_SET_PIN_SCREEN = 722;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=16896%3A72866&t=nRbFU86jEoEPH14b-0
  CC_EMI_VIEW_ACTIVE_SCREEN = 723;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9964%3A43930&t=jqxjczLXGkoBZD15-0
  CC_SUCCESS_INFO_DIALOG_SCREEN = 724;
  //  https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=7375%3A29396&t=H5VYHf5n2iX8LzJ8-0
  CC_INITIATE_SCREEN = 725;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=24225-92363&t=o1i95sLagG5q5yeX-0
  CC_LOUNGE_ACCESS_SCREEN = 726;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9560%3A42186&t=URYN34rw4H90m85W-1
  CC_OTP_SCREEN = 727;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=6079%3A28065
  CC_QR_ACTIVATION_SCREEN = 728;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=9964%3A43390&t=QeFu4Mo7uS9l24Gk-00
  CC_SELECT_STATEMENT_DURATION_DIALOG_SCREEN = 729;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8987%3A41480
  CC_REQUEST_CUSTOM_REASONS_SCREEN = 730;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=14298%3A60202&t=bwOQTI4B5wq6JnKc-0
  CC_STATEMENT_DETAILS_SCREEN = 731;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8977%3A38866
  CC_NEW_REQUEST_SCREEN = 732;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8977%3A39331
  CC_REQUEST_REASONS_SCREEN = 733;
  // https://www.figma.com/file/UO85a3b8ayLW9ZSFPhzRLi/CC-Rewards?node-id=841%3A24887&t=D7P20hHBcVc2FB1v-1
  CC_THREE_TOP_REWARDS_SCREEN = 734;
  // https://www.figma.com/file/UO85a3b8ayLW9ZSFPhzRLi/CC-Rewards?node-id=813%3A33344&t=ECIIGBnCeqXjplGj-1
  CC_EXTRA_REWARDS_DETAILS_DIALOG_SCREEN = 735;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&mode=design&t=FWtpsPZoXO2AqqxS-0
  CC_AUTH_POLL_SCREEN = 736;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=6530-41211&mode=design&t=mNjTX7T6SuHlx5yO-4
  CC_GENERIC_HALT_SCREEN = 737;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=7303-23078&mode=design&t=7fnFmT6QTRZhjnYH-0
  CC_KNOW_MORE_SCREEN = 738;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8219-38725&mode=design&t=eWsbZLvgaMCbDAwY-0
  CC_LIVENESS_SUMMARY_POLL_SCREEN = 739;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=7577-33283&mode=design&t=mNjTX7T6SuHlx5yO-4
  CC_PERMANENT_USER_FAILURE_SCREEN = 740;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8219-38725&mode=design&t=WYc6YnBGbJRPHNyM-0
  CC_REWARDS_POLL_SCREEN = 741;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11852-55825&mode=design&t=yXHu61e3JnMk36XP-0
  CC_DISPUTE_DETAILS_SCREEN = 742;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11852-55825&mode=design&t=iLQw0iza1lYKwequ-0
  CC_DISPUTE_DIALOG_SCREEN = 743;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=11852-56301&mode=design&t=rvK0bjihgibWowKF-0
  CC_DISPUTE_QUESTIONS_SCREEN = 744;
  //https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=6350%3A67623&t=NXDJ5qRMyIHocAho-0
  CC_DISPUTE_SUMMARY_SCREEN = 745;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=8219-38725&mode=design&t=bd035CoA56vIw7cH-0
  CC_STATUS_POLL_SCREEN = 746;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8219%3A40372
  CC_ENABLE_PAYMENTS_SCREEN = 747;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=17723-74334&t=fDkO48GHuLIDsnhm-0
  CC_CONFIRM_SELECTION_DIALOG_SCREEN = 748;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=19887-78816&mode=design&t=pL2CWq6O0t0h9sPY-0
  CC_WAITLISTED_SCREEN = 749;
  //https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=19887-78816&mode=design&t=pL2CWq6O0t0h9sPY-0
  CC_WAITLIST_SCREEN = 750;
  // Screen to decide the onboarding flow (which product/feature) depending on intent
  // https://www.figma.com/file/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?type=design&node-id=19488-11008&mode=design&t=AnlM2qeucAxI9nF3-0
  ONBOARDING_INTENT_SELECTION = 751;
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=317%3A28334&mode=dev
  NET_WORTH_DEPOSITS_CA_NO_ACC_DISCOVERED_SCREEN = 752;
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=317-27417&mode=design&t=qEocfuXx66ZPkul3-4
  NET_WORTH_DEPOSITS_SCREEN = 753;
  //Screen for BKYC consent
  BKYC_CONSENT_SCREEN = 754;
  //Screen for BKYC Handshake
  BKYC_HANDSHAKE_SCREEN = 755;
  //Screen for BKYC user details fetched
  BKYC_USER_DETAILS_SCREEN = 756;
  //Screen for BKYC selection choice
  BKYC_CHOICE_SCREEN = 757;
  //Screen for generic erro
  DEFAULT_ERROR_SCREEN = 758;
  // Fi lite lending - name and gender form screen. https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-32126&mode=dev
  PL_NAME_GENDER_SCREEN = 759;
  // Fi lite lending - PAN and DOB form screen. https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-32242&mode=dev
  PL_PAN_DOB_SCREEN = 760;
  // Fi lite lending - bank details form screen for penny drop. https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-32913&mode=dev
  PL_BANKING_DETAILS_SCREEN = 761;
  // Fi lite lending - credit report check screen. https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31729&mode=dev
  PL_CREDIT_REPORT_FETCH_CONSENT_SCREEN = 762;
  // Fi lite lending - landing page check eligibility screen. https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=27387-8565&mode=dev
  PL_LOAN_ELIGIBILITY_LANDING_SCREEN = 763;
  // Loan Application/Eligibility Review details screen with all the application data filled by the user till now
  PL_APPLICATION_REVIEW_DETAILS_SCREEN = 764;
  // Loan Application/Eligibility loading screen after submission of all the application data filled by the user
  PL_CHECK_ELIGIBILITY_LOADING_SCREEN = 765;
  // Loan offer available success screen - https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=26656-31262&mode=dev
  PL_ELIGIBILITY_OFFER_AVAILABLE_SCREEN = 766;
  // Loan offer not available screen - https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=29325-52592&mode=dev
  PL_ELIGIBILITY_NO_OFFER_AVAILABLE_SCREEN = 767;
  // non qualified user loan dashboard screen - https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=7697-26950&mode=design&t=ohlCCexI5CqtVe8n-0
  PL_NON_ELIGIBLE_USER_LANDING_SCREEN = 768;

  // Screen for credit card intro https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-20302&mode=dev
  CC_HORIZONTAL_INTRO = 769;

  // Figma: https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=9701-42332&mode=design&t=2EHzNmCp0NDQRrgc-4
  CC_AMPLI_FI_SCREEN = 770;
  // Credit card ineligible user screen - https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=2539-20531&mode=design&t=G1MP0tw5tHn8d1n0-0
  CC_INELIGIBLE_USER_SCREEN = 771;
  // https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?type=design&node-id=34462-59053&mode=design&t=XFj9NqLrmbQmddK2-0
  ONBOARDING_SAVINGS_ACCOUNT_INTRO = 772;
  // Client calls the ResetOnboardingStage RPC
  RESET_ONBOARDING_STAGE_RPC = 773;

  // Networth Dashboard screen - https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-•-FFF?node-id=157%3A13236&mode=dev
  NETWORTH_DASHBOARD_SCREEN = 774;
  // this deeplink calls a rpc with mentioned payload and redirects based on next_action sent back.
  // the rpc being called should have a next_deeplink in response.
  RPC_BASED_REDIRECTION = 775;
  // // https://www.figma.com/file/x1hL90FILdP836CGYpOQwZ/Fi-lite-Onboarding?type=design&node-id=2267-87461&mode=design&t=319NTHmrko9s6Foy-4
  PL_ACQ_TO_LEND_LANDING_SCREEN = 776;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30915-43461&mode=dev
  PL_EDIT_LOAN_BOTTOM_SHEET = 777;
  // LoansInfoScreenOptions used as a generic intro/error/popup screen
  // which might have center image, title, desc, bullet points, term infos, Cta for deeplink navigation
  // e.g.-
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=31119-26825&mode=design&t=6T29915B1N6Q7zST-4
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=29358-62846&mode=design&t=6T29915B1N6Q7zST-4
  LOANS_INFO_SCREEN = 778;
  // Screen/bottomsheet to show details on how to unlock a locked reward
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=17542-92350&mode=design&t=fcwnnWfhsNsHnDKr-0
  REWARD_UNLOCK_DETAILS_BOTTOMSHEET = 779;
  // Screen to show details about and offer being redeemed
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=14751-85330&mode=design&t=fcwnnWfhsNsHnDKr-4
  OFFER_REDEEM_INFO_BOTTOMSHEET = 780;
  // Screen for starting the Vistara offer conversion flow
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=14849-83928&mode=design&t=<DUfDdqpjthYBBlVz-4></DUfDdqpjthYBBlVz-4>
  OFFER_VISTARA_CONVERSION_BOTTOMSHEET = 781;
  // Screen for entering the user details for Vista offer conversion/redemption
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=14849-84668&mode=design&t=DUfDdqpjthYBBlVz-4
  OFFER_VISTARA_ENTER_DETAILS_BOTTOMSHEET = 782;
  // A dialog screen to select the addess for offer redemption
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=3905-0&mode=design&t=DUfDdqpjthYBBlVz-4
  OFFER_SELECT_ADDRESS_DIALOG = 783;
  // Screen for calculator functionality for Salary
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=9203%3A75647&t=qQTT97Bl6HdZlPD8-4
  SALARY_CALCULATOR_SCREEN = 784;
  // Screen for enabling permissions required for Early salary loans
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/Pre-approved-loans-%E2%80%A2-FFF?node-id=11188%3A12539&t=pqnX0EtVisnkLZwO-1
  ES_ENABLE_PERMISSIONS_SCREEN = 785;
  // Screen for referring colleagues for salary account
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=6465%3A65794
  SALARY_REFERRAL_LANDING_SCREEN = 786;
  // Screen for Policy information for Salary
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=10078-79364&t=Zv5GKZKiOOk81zT4-4
  SALARY_POLICY_INFO_SCREEN = 787;
  // Screen for showing details about sending cancellation of Enach
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13350-98579&mode=design&t=iLgHFHqZGIouTBMI-4
  SALARY_ENACH_CANCEL_MADATE_SETUP_SCREEN = 788;
  // Screen for setting up user details for enach mandate setup
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13509-102092&mode=design&t=D2xF5EmgLckVOEmi-4
  SALARY_ENACH_MANDATE_SETUP_SCREEN = 789;
  // Screen for showing the status of the Enach mandate setup
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=12804-94550&mode=dev
  SALARY_ENACH_SETUP_STATUS = 790;
  // Screen for showing the tabs for introduction to Enach before starting the setup flow
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13233-121072&mode=design&t=i9kw6EswYXJuVlVl-4
  SALARY_ENACH_INTRO_SCREEN = 791;
  // Screen for showing the upgrade screen for Salary
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=15689-102488&mode=design&t=SGt3YBju8n0DJpjz-4
  SALARY_ENACH_FULL_UPGRADE_SCREEN = 792;
  // Screen for showing preview of the scanned cheque
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=10037-80096&t=DP9MDCaFW2Od3H9x-4
  CANCELLED_CHEQUE_IN_PREVIEW_SCREEN = 793;
  // Screen for showing the status of Salary verification after selecting the salary transaction
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=559-59310&mode=design&t=WpvHWitvXwZnFdOl-4
  SALARY_VERIFICATION_STATUS_SCREEN = 794;
  // Screen, shown as a dialog about the Coin amount pending/in processing
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=17542-92715&mode=design&t=<h1dzv7nA0hau5Kx5-0></h1dzv7nA0hau5Kx5-0>
  REWARD_PENDING_DETAILS_SCREEN = 795;
  // Screen to display a list/grid of user's unopened rewards
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=16524-90681&mode=design&t=ThSRcrfkIwhSjBPC-0
  REWARD_UNOPENED_LIST_SCREEN = 796;
  // screen that will be used while searching for FAQ categories
  // figma: https://www.figma.com/file/GGiPlpxpvYa2eHdg68m0pg/Help-%E2%80%A2-Workfile?type=design&node-id=6757-63692&mode=dev
  CX_CATEGORY_SEARCH_SCREEN = 797;
  // Screen for DC Order Revamp
  DC_PHYSICAL_ORDER_SCREEN = 798;
  // screen to show CX category details
  CX_CATEGORY_DETAILS_SCREEN = 799;
  // screen for showing dashboard for loans
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=447-10393&mode=design&t=AiaZVWMZruzMHTZW-0
  LOANS_DASHBOARD_SCREEN = 800;
  // screen for showing overview of a specific loan, such as emi details, loan interest rate, principal amt, paid amt etc.
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-15210&mode=design&t=AiaZVWMZruzMHTZW-0
  LOANS_OVERVIEW_SCREEN = 801;
  // screen for showing all the details regarding the loan
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-15163&mode=design&t=AiaZVWMZruzMHTZW-0
  LOANS_DETAILS_SCREEN = 802;
  // screen for showing loan payment details and upcoming emi schedule.
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=1304-13603&mode=design&t=AiaZVWMZruzMHTZW-0
  LOANS_PAYMENT_DETAILS_SCREEN = 803;
  // screen for selecting employer for loans
  PL_EMPLOYER_SELECTION_SCREEN = 804;
  // screen for location permissions for loans
  PL_LOCATION_PERMISSION_SCREEN = 805;
  // HelpLanding V2
  HELP_LANDING_SCREEN_V2 = 806;
  // loading screen that triggers otp generation for mf holdings import https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3927&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_GENERATE_OTP_SCREEN = 807;
  // screen to input mf holdings import otp https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3810&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_OTP_SUBMISSION_SCREEN = 808;
  // screen to show import progress post otp submission https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-4143&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_OTP_SUCCESS_LOADING_SCREEN = 809;
  // screen for user to manually enter phone number for mf holdings import https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3945&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_PHONE_NUMBER_SUBMISSION_SCREEN = 810;
  // screen for user to manually enter email for mf holdings import https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3992&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_EMAIL_SUBMISSION_SCREEN = 811;
  // mutual fund import consent screen v2 https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13988-3759&mode=design&t=kSKONPbGKIO38wtR-4
  MF_HOLDINGS_IMPORT_CONSENT_SCREEN_V2 = 812;
  // This screen will be used to prompt user to connect a bank account.
  // E.g. In Fi Lite Pay, if no account is connected then user will be prompted
  // to connect a bank account which then could be used to Pay.
  // figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=19320-18968&mode=design&t=Sval55XxIC9inTWk-0
  CONNECT_BANK_ACCOUNTS_INTRO_SCREEN = 813;
  // https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=15143-18604&mode=design&t=woYX9XiNBN1oObov-0
  // screen to upload FILE
  UPLOAD_FILE = 814;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153%3A35008
  // US Stocks Wallet Landing Screen
  US_STOCKS_WALLET_LANDING_SCREEN = 815;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153%3A37080&mode=design&t=ZSndRrtzjFyIUnvB-1
  // US Stocks Wallet Add Funds Screen
  US_STOCKS_WALLET_ADD_FUNDS_SCREEN = 816;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153%3A37003&mode=design&t=ZSndRrtzjFyIUnvB-1
  // US Stocks Wallet Withdraw Screen
  US_STOCKS_WALLET_WITHDRAW_SCREEN = 817;
  //
  LAMF_LANDING_SCREEN = 818;

  LAMF_JOURNEY_SCREEN = 819;

  LAMF_ADD_ACCOUNT_DETAILS_USER_INPUT_SCREEN = 820;

  LAMF_CAMS_PORTFOLIO_FETCH_OTP_VERIFICATION_SCREEN = 821;

  LAMF_KARVY_PORTFOLIO_FETCH_OTP_VERIFICATION_SCREEN = 822;

  LAMF_VIEW_OFFER_SCREEN = 823;

  LAMF_OFFER_SELECTION_SCREEN = 824;

  LAMF_APPLICATION_DETAIL_SCREEN = 825;

  LAMF_APPLICATION_ADDITIONAL_DETAILS_USER_INPUT_SCREEN = 826;

  LAMF_KYC_SCREEN = 827;

  LAMF_ESIGN_SCREEN = 828;

  LAMF_MANDATE_SCREEN = 829;

  LAMF_CAMS_LIEN_MARK_OTP_VERIFICATION_SCREEN = 830;

  LAMF_KARVY_LIEN_MARK_OTP_VERIFICATION_SCREEN = 831;

  LAMF_LIEN_PROGRESS_UPDATE_SCREEN = 832;

  LAMF_ESIGN_PROGRESS_UPDATE_SCREEN = 833;

  LAMF_MANDATE_INTRO_SCREEN = 834;

  LAMF_APPLICATION_SUCCESS_SCREEN = 835;

  LAMF_OFFER_FUND_DETAILS_SCREEN = 836;

  LAMF_INSUFFICIENT_FOLIO_ERROR_SCREEN = 837;

  LAMF_PARTIAL_LIEN_MARK_ERROR_SCREEN = 838;

  LAMF_PERMANENT_FAILURE_FULL_SCREEN = 839;

  LAMF_HOLDING_SCREEN = 840;

  LAMF_DASHBOARD_SCREEN = 841;

  LAMF_LOAN_OVERVIEW_SCREEN = 842;

  LAMF_PAYMENT_DETAILS_SCREEN = 843;

  LAMF_LOAN_DETAILS_SCREEN = 844;

  LAMF_INITIATE_SI_SETUP_SCREEN = 845;

  LAMF_PRE_PAY_SCREEN = 846;

  LAMF_FUND_ELIGIBILITY_SCREEN = 847;

  LAMF_NO_FUNDS_FOUND_FAILURE_SCREEN = 848;

  // Figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=36659-29100&mode=dev
  CC_LOUNGE_ACCESS_V2_SCREEN = 849;
  // Figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=27502-82619&mode=design&t=ictcVPmkK7Bzz1wL-4
  CC_COLLECTED_LOUNGE_PASSES_SCREEN = 850;
  // Screen to take selfie image for lending flows
  LOANS_SELFIE_SCREEN = 851;
  // Screen to collect form data from user
  LOANS_FORM_DETAILS_SCREEN = 852;
  // Preapproved Loan S1 - shows the loan offer info - V2
  PL_OFFER_DETAILS_V2 = 853;
  // PL S2 - Shows application info and the Terms and conditions page - V2
  PL_APPLICATION_DETAILS_V2 = 854;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10082-40718&mode=design&t=fEGbs85eYqgpYdX1-0
  LOANS_INCOME_VERIFICATION_INTRO_SCREEN = 855;
  //  https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=11201-113965&mode=<dev></dev>
  CA_FI_TO_FI_POLLING_SCREEN = 856;
  // https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=10505-120314&mode=dev
  CA_FI_TO_FI_TERMINAL_SCREEN = 857;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10082-40545&mode=design&t=fEGbs85eYqgpYdX1-0
  LOANS_INCOME_VERIFICATION_RESULT_SCREEN = 858;
  // Figma link: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=35086-65441&mode=design&t=Jcs4ENky4VNTTl5e-4
  CREDIT_CARD_BILLING_DETAILS_BOTTOM_VIEW_SCREEN = 859;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011%3A26702&mode=dev
  US_STOCKS_ONBOARDING_RISKY_PROILE_TERMINAL_SCREEN = 860;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011%3A25979&mode=dev
  US_STOCKS_ONBOARDING_CREATE_PROFILE_SCREEN = 861;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011%3A26228&mode=dev
  US_STOCKS_ONBOARDING_COLLECT_RISK_LEVEL_SCREEN = 862;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21202%3A16924&mode=dev
  US_STOCKS_ONBOARDING_RISK_DISCLOSURE_SCREEN = 863;
  // https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4743-29973&mode=design&t=61MUz6ktBYAlcm8Z-4
  CC_ALL_ELIGIBLE_CARDS_SCREEN = 864;
  // https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4751-30530&mode=design&t=BwXyZn6Xce97eOjp-4
  CC_NETWORK_SELECTION_SCREEN = 865;
  // Intermediate screen to be shown to the user for landing them on the PWA flow.
  // On receiving a deeplink with his screen name, the client needs to call the GetPwaDeeplink Loans FE rpc with the request params (loan_request_id/loan_account_id) to get appropriate PWA deeplink for redirecting to the PWA flow.
  // screen_options: api.typesv2.deeplink_screen_option.preapprovedloans.LoansPWALandingScreenOptions
  LOANS_PWA_LANDING_SCREEN = 866;
  // screen to redirect the user to a PWA flow, the screen options contain a web url which the client should open in a web view for redirecting the user to the PWA flow.
  // screen_options: api.typesv2.deeplink_screen_option.pkg.PWARedirectionScreenOptions
  // sample pwa screen figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10651-39414&mode=design&t=HaJNCIwtunJUHdcu-0
  PWA_REDIRECTION_SCREEN = 867;
  // screen to display bottom sheet for loan's usecases.
  // screen_options: api.typesv2.deeplink_screen_option.pkg.LoansBottomSheetScreenOptions
  // figma : https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10681-40109&mode=design&t=HaJNCIwtunJUHdcu-0
  LOANS_BOTTOM_SHEET_SCREEN = 868;
  // ApplyForLoan() Rpc will be called whenever client receives this deeplink
  // screen_options: api.typesv2.deeplink_screen_option.pkg.ApplyForLoanScreenOptions
  APPLY_FOR_LOAN_SCREEN = 869;

  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9767-33940&mode=design&t=9JUf66Uqq9yVYQbc-0
  PL_INITIATE_MANDATE_V2_SCREEN = 870;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10204-50117&mode=design&t=9JUf66Uqq9yVYQbc-0
  PL_ALTERNATE_ACCOUNTS_SCREEN = 871;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10204-50199&mode=design&t=9JUf66Uqq9yVYQbc-0
  PL_MANDATE_SETUP_SCREEN = 872;

  // benefit screen for saving account closure
  // Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-3384&mode=design&t=CRwxtzdEnhr4kgl6-4
  SA_CLOSURE_BENEFIT_SCREEN = 873;

  // feedback screen for account closure
  // Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-3622&mode=design&t=QaLHIsX3YnmLnY1U-4
  SA_CLOSURE_FEEDBACK_SCREEN = 874;

  // crrieria screen for account closure
  // Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-3892&mode=design&t=HTKzSO6UMNfip928-4
  SA_CLOSURE_CRITERIA_SCREEN = 875;

  // pan dob screen for account closure
  // Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-3660&mode=design&t=HTKzSO6UMNfip928-4
  PAN_DOB_INPUT_SCREEN = 876;

  // conform account closure screen
  // Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-4878&mode=design&t=HTKzSO6UMNfip928-4
  SA_CLOSURE_SUBMIT_REQUEST_SWIPE_ACTION_SCREEN = 877;

  // info screen for account closure user for credit/debit/all freeze
  // Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-3783&mode=design&t=HTKzSO6UMNfip928-4
  FULL_SCREEN_INFO_VIEW_SCREEN = 878;

  // resolve screen for account closure
  // Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-4860&mode=design&t=HTKzSO6UMNfip928-4
  SA_CLOSURE_RESOLVE_ISSUE_SCREEN = 879;

  // request submitted screen for account closure
  // Figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1-4878&mode=design&t=HTKzSO6UMNfip928-4
  SA_CLOSURE_REQUEST_SUBMITTED_SCREEN = 880;

  // mf holding import progress screen
  //https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-•-FFF?node-id=13988%3A4143&mode=dev
  MF_HOLDINGS_IMPORT_PROGRESS_SCREEN = 881;

  // shows a loader screen for the given time
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=11980-39468&mode=design&t=aY7iBNs7wWpacNRY-4
  LOANS_TIMED_LOADER_SCREEN = 882;

  // screen to show tiering success screen in onboarding add funds flow
  // figma: https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=271-4523&mode=design&t=6QdRNpZBHIWJfxn7-0
  ONBOARDING_ADD_FUNDS_TIERING_SUCCESS_SCREEN = 883;

  LAMF_KFS_SCREEN = 884;
  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=12418-32528&mode=design&t=7lvO50N6k4ZpCffp-4
  LOANS_WEBVIEW_DIGILOCKER = 885;
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=6865-40084&mode=design&t=1WdaJupvl7JxkUjv-0
  LAMF_FUND_VERIFICATION_FAILURE_SCREEN = 886;
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=796-18746&mode=design&t=SLnAF9lfdz8B4bsq-0
  LAMF_VERIFY_MF_PROGRESS_UPDATE_SCREEN = 887;
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=6724-36820&mode=design&t=SLnAF9lfdz8B4bsq-0
  LAMF_LOAN_DETAIL_VERIFICATION_SCREEN = 888;

  // https://www.figma.com/file/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=6213%3A146440&t=yCYsZOpAcDpVohlP-4
  DC_PHYSICAL_BENEFITS_SCREEN = 889;
  // https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=9387-35619&mode=design&t=nev9Kv61kmKaae8H-0
  DC_CHECK_STATUS_FLOW_SCREEN = 890;
  // https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=9387-35619&mode=design&t=nev9Kv61kmKaae8H-0
  DC_INITIATE_FLOW_SCREEN = 891;
  // https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=10214%3A38287&t=ysv2mA1h1Im9yPs7-4
  ORDER_CARD_SUCCESS_SCREEN = 892;
  // Screen to show the success message once the workflow for new card request is completed
  NEW_CARD_REQUEST_SUCCESS_SCREEN = 893;

  DC_RESET_ATM_PIN_OPTIONS_SCREEN = 894;
  // Polling fragment for RPC : getRequestStatusSync
  CC_SYNC_POLL_SCREEN = 895;
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=7183%3A22787
  CC_STATION_HALT_SCREEN = 896;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=38152-40247&mode=design&t=ALXMmmJXnlKNlvQi-4
  LOANS_LANDING_SCREEN_V2 = 897;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=38152-40190&mode=design&t=ALXMmmJXnlKNlvQi-4
  LOANS_AMOUNT_SELECTOR_SCREEN = 898;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=38152-40313&mode=design&t=ALXMmmJXnlKNlvQi-4
  LOANS_DETAILS_SELECTION_V2 = 899;
  // Card details screen for reset pin via debit card
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=8022%3A71005&mode=design&t=h58zOd3dgZ3DMmrO-1
  RESET_PIN_CARD_DETAILS_SCREEN = 900;
  // Generic Terminal screen for UPI related use cases like UPI International
  // Figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?type=design&node-id=14063-93268&t=lPHC9WiC60P4BVxa-0
  PAY_TERMINAL_STATUS_SCREEN = 901;
  // ENach Management Polling Screen
  RECURRING_PAYMENT_ACTION_STATUS_SCREEN = 902;
  // External Enach management screen
  RECURRING_PAYMENT_WEB_VIEW_SCREEN = 903;
  // Pin set screen v2 post Aadhar OTP implementation
  // https://www.figma.com/file/xzcr5E8lsH1ihsh3RmqQTU/Pay-%E2%80%A2-FFF-%E2%80%A2--v1.3?node-id=9929-23547&t=8S4mD3lPzpg13mFN-0
  NPCI_PIN_SET_SCREEN_V2 = 904;
  // Post VPA Migration Success Screen in non-unified flow
  // figma.com/file/xzcr5E8lsH1ihsh3RmqQTU/Pay-•-FFF-•--v1.3?node-id=9-15383&t=jLxnxQvPowcQR8QI-0
  VPA_MIGRATION_SUCCESS_SCREEN_V1 = 905;
  // TPAP Bank Selection screen
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=5833%3A60144
  TPAP_BANK_SELECTION_SCREEN = 906;
  // TPAP Account Selection screen
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=5833%3A60135&t=GnIKPHkckqtiS4Sk-0
  TPAP_ACCOUNT_SELECTION_SCREEN = 907;
  // TPAP Self Transfer screen
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=6429%3A68236&t=ywAju9MzCCx4CjzK-0
  TPAP_SELF_TRANSFER_SCREEN = 908;
  // TPAP Linking success screen
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=7723%3A71399
  TPAP_LINKING_SUCCESS_SCREEN = 909;
  // Unified TPAP flow VPA Migration Intro Screen
  VPA_MIGRATION_INTRO_SCREEN = 910;
  // Unified TPAP flow VPA Migration Success Screen
  VPA_MIGRATION_SUCCESS_SCREEN_V2 = 911;
  // Screen where user can select an account to enable international UPI payments via QR scanning flow
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?type=design&node-id=14051-94034&t=AsIo4odA5dJP63eu-0
  UPI_INTERNATIONAL_ACTIVATION_SCREEN = 912;
  // Screen where user will enter amount and select currency for international UPI payments
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?type=design&node-id=14128-99426&t=H03zUCoNFz3moSUN-0
  UPI_INTERNATION_ENTER_AMOUNT_SCREEN = 913;
  // UPI Tips videos screen
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=12848-87062&t=IEIwA9WMq43ehOpK-0
  UPI_TIPS_SCREEN = 914;
  // Intro screen for UPI Mapper
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=10600%3A82987&t=8SQcht9rnIJLnPgQ-0
  UPI_MAPPER_INTRO_SCREEN = 915;
  // Details screen for UPI Mapper
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=9782%3A84002&t=88mgI7GF6ZKOySSA-0
  UPI_MAPPER_DETAILS_SCREEN = 916;
  // Add money screen for UPI Lite
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/Pay-%2F-Workfile?node-id=16172%3A99492&mode=dev
  UPI_LITE_ADD_MONEY_SCREEN = 917;
  // Introduction screen for UPI Lite
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=15433%3A98560
  UPI_LITE_INTRO_SCREEN = 918;
  // Pay x AskFi Search Screen
  PAY_ASK_FI_SEARCH_SCREEN = 919;
  // Screen for entering the Fi coins for Vendor points conversion
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=14849-83928&mode=design&t=g2ZiSUaQJv4koNz2-4
  OFFER_FI_COINS_CONVERSION_BOTTOMSHEET = 920;
  // Screen for entering the user details for Vista offer conversion/redemption
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=14849-84668&mode=design&t=DUfDdqpjthYBBlVz-4
  OFFER_FI_COINS_ENTER_DETAILS_BOTTOMSHEET = 921;
  // Screen for redirecting users for Employer doc submission:
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=540%3A85908
  SALARY_EMPLOYER_DOC_SUBMISSION_BOTTOMSHEET = 922;
  // Screen for searching Employer names
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=11867-84275&mode=dev
  SALARY_EMPLOYER_SEARCH_BOTTOMSHEET = 923;
  // Salary verification success/confirmed screen
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=540%3A81469
  SALARY_EMPLOYER_CONFIRMATION_BOTTOMSHEET = 924;
  // Salary verification failure bottomsheet:
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=12143-89950&t=04g8kygIhf0ip4PJ-4
  SALARY_EMPLOYER_VERIFICATION_FAILED_BOTTOMSHEET = 925;
  // Screen for showing Salary-Enach benefits
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13233-121074&mode=design&t=aoCYiKyHtt7ejKF7-4
  SALARY_ENACH_BENEFITS_SCREEN = 926;
  // https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=6724-36851&mode=design&t=laf3rWXOSA2nC4mh-4
  LAMF_FUND_VERIFICATION_BOTTOM_SHEET = 927;
  // https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=9483-107713&mode=dev
  AA_CONSENT_RENEWAL_DIALOG = 928;
  // To start connected SDK for renewal flow
  AA_CONSENT_RENEWAL_SDK_SCREEN = 929;
  // To start connected SDK for connect flow
  AA_SDK_SCREEN = 930;
  // To start connected SDK for fi to fi flow
  AA_FI_TO_FI_SDK_SCREEN = 931;
  // Post consent approval, polling screen
  AA_POLLING_SCREEN = 932;
  // Post polling terminal screen
  AA_TERMINAL_SCREEN = 933;
  // Reoobe screen, when users phone number changes and want to connect account
  AA_REOOBE_SCREEN = 934;
  // Transaction coming soon, when for a particular bank/entity transaction not enabled
  AA_TRANSACTION_COMING_SOON = 935;
  // Reports and Downloads on profile screen
  PROFILE_REPORTS_DOWNLOADS = 936;
  // Service request on profile screen
  PROFILE_SERVICE_REQUESTS = 937;
  // Cheque book order details
  // https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=7365%3A32090&t=oPnJNmnahjMAOhbj-0
  CHEQUE_BOOK_ORDER_DETAILS = 938;
  // Legality sign flow on profile screen
  // https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=9281-35326&t=OIbXcnFeyqxSKgsu-4
  LEGALITY_SIGN_FLOW = 939;
  // Order cheque book on profile screen
  // https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=7365%3A32164&t=kwGVXjf1ioZorS9a-0
  ORDER_CHEQUEBOOK = 940;
  // Savings sign prompt on profile screen
  // https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=9589-37949&t=OIbXcnFeyqxSKgsu-4
  SAVINGS_SIGN_PROMPT = 941;
  // Polling screen after placing a chequebook request
  // https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=7365%3A32616&t=NJxprTu4lXJmLO7r-0
  CHEQUE_BOOK_POLLING_SCREEN = 942;
  // https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=7365-32528&t=KTbGNKzOF76g92hj-4
  ALFRED_REQUEST_SUMMARY = 943;
  // Export logs screen
  EXPORT_HEALTH_LOG_SCREEN = 944;
  // Open source licenses
  PROFILE_OPEN_SOURCE_LICENSES = 945;
  // Notification settings screen on profile screen
  PROFILE_SETTINGS_NOTIFICATION = 946;
  // Enter VPA dialog from Onboarding add funds and add funds
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1586-63113&mode=dev
  DIALOG_ENTER_VPA = 947;
  // Payment method selection, bottom sheet
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=2083-54863&mode=design&t=GgW16c26T4XzKVKT-4
  PAYMENT_METHOD_SELECTION_BOTTOM_SHEET = 948;
  // Personal details edit
  PROFILE_PERSONAL_DETAILS_EDIT = 949;
  // Profile crop photo
  PROFILE_EDIT_IMAGE_SCREEN = 950;
  // Profile selection dialog is showing all the options for changing or deleting pic from user profile
  PROFILE_DIALOG_EDIT_OPTION = 951;
  // Screen used to show Information like DOB and address that going to be updated
  // https://www.figma.com/file/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?type=design&node-id=12733-15559&mode=design&t=KFWusVBmRQXnEaz2-4
  INFO_ACKNOWLEDGEMENT_V2 = 952;
  // https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=13015-57250&mode=design&t=UQTeuem2EFxku9TQ-0
  CC_CREDIT_REPORT_ADDRESS_SCREEN = 953;
  // https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-AmpliFi-Credit-Card-%E2%80%A2-FFF?type=design&node-id=6226-23209&mode=design&t=qRSwyCrbsNCWRuU4-0
  //  // screen to collect the consent from the user on the intro credit card screen
  CC_CONSENT_BOTTOM_SHEET = 954;
  // analytics screen name for loans esign webview flow
  LOANS_ESIGN_WEBVIEW = 955;
  // analytics screen name for loans digio sdk
  LOANS_MANDATE_DIGIO_SDK = 956;

  ADD_ADDRESS = 957;
  EKYC_CONSENT = 958;
  NAME_DOB_MISMATCH = 959;
  EKYC_VERIFICATION = 960;
  LIVENESS = 961;
  LIVENESS_POLLING = 962;
  ONB_ADD_FUNDS = 963;
  ONB_ADD_MONEY = 964;
  ONB_ADD_FUNDS_TIERING_SUCCESS = 965;
  FI_BENEFITS = 966;
  ATM_PIN_VERIFICATION = 967;
  ACC_DELETION_ACK = 968;
  LIVENESS_MANUAL_VERIFICATION = 969;
  LOGIN_VERIFICATION_ERROR = 970;
  FINITE_CODE_VERIFICATION = 971;
  INCREASE_BONUS_FOR_AFFLUENT_USER = 972;
  RE_KYC_SCREEN = 973;
  PAN_AUTH_SCREEN = 974;
  AFU_POLL = 975;
  ACC_CREATION_POLL = 976;
  ONB_NEXT_ACTION_POLL = 977;
  CREDIT_REPORT_AVAILABILITY = 978;
  CREDIT_REPORT_VERIFICATION = 979;
  EMPLOYMENT_VERIFICATION = 980;
  SCREENER_ERROR = 981;
  SCREENER_REJECT_STATE = 982;
  GMAIL_VERIFICATION_STATUS = 983;
  LINKEDIN_VERIFICATION = 984;
  SIGN_OUT_TRANSITION = 985;
  VKYC_NEXT_ACTION = 986;
  VKYC_REGISTER_NOTIFICATION = 987;
  VKYC_HEADLESS = 988;
  EPAN_INIT = 989;
  VKYC_INSTRUCTION_OVERLAY = 990;
  VKYC_SCHEDULED_ACK = 991;

  // Screen in lending flow to fetch debit card details required to complete mandate step
  LOANS_DEBIT_CARD_DETAILS_SCREEN = 992;
  DC_CARD_RENEWAL_TYPE_SELECTION_SCREEN = 993;

  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5456&mode=dev
  EPF_PASSBOOK_IMPORT_CONFIRM_PHONE_NUMBER_SCREEN = 994;
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5426&mode=dev
  DMF_GENERIC_LOADING_SCREEN = 995;
  //https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5405&mode=dev
  EPF_PASSBOOK_IMPORT_UAN_LIST_SCREEN = 996;
  //https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5790&mode=dev
  EPF_PASSBOOK_IMPORT_OTP_SCREEN = 997;
  //https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5604&mode=dev
  EPF_DASHBOARD_SCREEN = 998;

  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=3712%3A52671&mode=dev
  MANUAL_ASSET_FORM_SCREEN = 999;
  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=3712%3A51904&mode=dev
  MANUAL_ASSET_DASHBOARD_SCREEN = 1000;

  // https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?node-id=1%3A418&mode=dev
  INDIAN_STOCKS_DASHBOARD_SCREEN = 1001;
  // https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?node-id=33%3A3017&mode=dev
  INDIAN_STOCKS_INSTRUMENT_DETAILS_SCREEN = 1002;
  // https://www.figma.com/file/cgVb3yUeGGELbA6xGiUo1c/%F0%9F%87%AE%F0%9F%87%B3-Ind.-Stocks?node-id=33%3A209&mode=dev
  INDIAN_STOCKS_ORDER_RECEIPT_SCREEN = 1003;
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1780-149208&mode=design&t=Chl75i0hAJT6OTpc-4
  ADD_FUND_UPGRADE_UPI_BOTTOM_SHEET = 1004;

  // Onboarding screen names
  ADD_FUNDS_ONB_V1_SUCCESS_TRANSITION_SCREEN = 1005;
  AFFLUENT_TRANSITION_SCREEN = 1006;
  FEATURE_BENEFITS_SCREEN = 1007;
  GENERIC_BOTTOMSHEET_SCREEN = 1008;
  EPAN_ENTRYPOINT_SCREEN = 1009;
  GENERIC_TRANSITION_SCREEN = 1010;
  ACCOUNT_DELETION_SCREEN = 1011;
  SCREENER_BOTTOMSHEET_SCREEN = 1012;
  GENERIC_RECORD_CONSENT_SCREEN = 1013;
  DEVICE_SECURITY_LOCK_SCREEN = 1014;
  NEXT_ONBOARDING_ACTION_SCREEN = 1015;
  ONBOARDING_PERMISSIONS_SCREEN = 1016;
  CKYC_DROPOFF_BOTTOMSHEET_SCREEN = 1017;
  VKYC_REVIEW_SCREEN = 1018;
  VKYC_LOADING_SCREEN = 1019;
  VKYC_WAITING_SCREEN = 1020;
  VKYC_VERIFYING_DOCS_SCREEN = 1021;
  VKYC_REJECTED_SCREEN = 1022;
  VKYC_AGENTS_BUSY_SCREEN = 1023;
  VKYC_UPLOAD_DOCUMENT_SCREEN = 1024;
  VKYC_INSTRUCTIONS_V2_SCREEN = 1025;
  EKYC_STATUS_SCREEN = 1026;
  VKYC_RETRY_CALL_SCREEN = 1027;
  VKYC_INSTRUCTIONS_V1_SCREEN = 1028;
  VKYC_LANDING_V2_SCREEN = 1029;
  VKYC_PANCARD_TYPE_SELECTION_SCREEN = 1030;
  VKYC_NOTIFY_ME_SCREEN = 1031;
  CONFIRM_PHONE_NUMBER_BOTTOMSHEET = 1032;
  UPDATE_USER_DETAILS_SCREEN = 1033;
  PHONE_VERIFICATION_OTP_SCREEN = 1034;
  LIVENESS_SUCCESS_SCREEN = 1035;
  AUTH_FACTOR_UPDATE_STATUS_SCREEN = 1036;
  APPLE_SIGNIN_MISSING_EMAIL_SCREEN = 1037;
  DEVICE_LOCK_SCREEN = 1038;
  CIBIL_OTP_VERIFICATION = 1039;
  ENTER_MOBILE_NUMBER = 1040;


  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=11903-30010&t=r0Nn2ivQrNSsQfNY-4
  US_STOCK_INTRO = 1041;
  //https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011-26091&mode=design&t=mBl3pG8kg0o2On3o-0
  US_STOCK_ONB_DROPDOWN = 1042;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1196%3A5514
  US_STOCK_ACTIVITY_SCREEN = 1043;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=5331%3A22214&t=8Ddx6YfGyOkgQke0-4
  US_STOCK_REMITTANCE_FORM_BOTTOM_SHEET = 1044;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1341%3A5375&mode=dev
  US_STOCK_CANCEL_ORDER_DIALOG = 1045;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A3852
  US_STOCK_ORDER_SUCCESS_SCREEN = 1046 [deprecated = true];
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=343%3A3991&mode=dev
  US_STOCK_ONB_PAN_UPLOAD_SUCCESS = 1047;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4428%3A19345&t=vMRKrMhk2B734aGn-4
  US_STOCK_PAN_RE_UPLOAD = 1048;

  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&mode=design&t=xw9BYIpOtC0d783p-0
  ANALYSER_BENEFITS_SCREEN = 1049;

  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13185-5709&mode=design&t=lIDQd7OItXucSJDF-0
  CREDIT_ANALYSER_DETAILS_BOTTOM_SHEET = 1050;
  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=1469-5094&mode=design&t=2b9Nj5pRILTBJ6OF-0
  CREDIT_ANALYSER_CONSENT_DIALOG = 1051;
  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=1469-5357&mode=design&t=qZfJs5B71tFu8ykb-0
  CREDIT_ANALYSER_OTP_DIALOG = 1052;

  // https://www.figma.com/file/KIOv6btvo1HwPNPP6j7sEb/Spend-Analyzer-%E2%80%A2-FFF?node-id=704%3A19750
  ANALYSER_FILTER_DIALOG = 1053;
  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=6077%3A16568&mode=dev
  ANALYSER_SINGLE_SELECT_FILTER = 1054;
  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=6077%3A16504&mode=dev
  ANALYSER_MULTI_SELECT_FILTER = 1055;
  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=809%3A5754&mode=dev
  ANALYSER_DURATION_FILTER = 1056;

  // https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=10202%3A24917&mode=dev
  MF_ANALYSER_LANDING_SCREEN = 1057;

  SIM_SELECTION_BOTTOM_SHEET = 1058;
  DEVICE_REGISTRATION_BOTTOM_SHEET = 1059;
  DEVICE_REGISTRATION_ERROR_BOTTOM_SHEET = 1060;
  // New deposit screen for secured card onboarding
  // Figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4021-9874&mode=design&t=AtwqdcJuU4BRleWh-0
  SECURED_CREDIT_CARD_DEPOSIT_SCREEN_V2 = 1061;

  // https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-•-FFF?type=design&node-id=1-5700&mode=design&t=wju1nFRF1IfDAMFd-0
  EPF_PASSBOOK_IMPORT_MANUAL_UAN_SCREEN = 1062;

  // Networth introduction screen - https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=1%3A5457&mode=dev
  NETWORTH_INTRO_SCREEN = 1063;
  // Networth asset types as a widget list - https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=157%3A19119&mode=dev
  NETWORTH_WIDGETS_LIST = 1064;
  // MF Auto Invest screen - https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=10-6159&mode=design&t=3UnaraRspBLbqmb5-0
  MF_AUTO_INVEST_SCREEN = 1065;
  // Rule activation success screen - https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=17566-60058&mode=design&t=jFLIqD2Vk02W2kl5-0
  FITTT_RULE_ACTIVATION_SUCCESS_SCREEN = 1066;
  // Mutual Funds One Time Investment Screen - https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=17566-58621&mode=design&t=jFLIqD2Vk02W2kl5-0
  MF_OTI_SCREEN = 1067;

  // Generic info popUp - https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?node-id=2543%3A67272&mode=dev
  INFO_POP_UP_V2 = 1068;

  GENERIC_OTP_SCREEN = 1069;

  //https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=12532-45540&mode=design&t=qOjAZnzsf53MZvnG-0
  DEPOSIT_SUMMARY_SCREEN = 1070;
  //https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=12933-47599&mode=design&t=qOjAZnzsf53MZvnG-0
  DEPOSIT_MATURITY_DATE_SELECTION = 1071;
  ADD_NOMINEE_SCREEN = 1072;
  ADD_NOMINEE_DETAILS_SCREEN = 1073;
  //https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=12995-48581&mode=design&t=KA6WtdIe214mlw3m-0
  DEPOSIT_DETAILS = 1074;
  DEPOSIT_STATEMENTS_SCREEN = 1075;
  //https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=12918-45155&mode=design&t=w3Oor9eUNxjlvgBX-0
  DEPOSIT_MATURITY_AND_PAYMENT_SELECTION = 1076;
  // https://www.figma.com/file/GGiPlpxpvYa2eHdg68m0pg/Help-•-Workfile?type=design&node-id=9504-63890&mode=design&t=ZOK0rtOMbUXIMBFL-0
  HELP_CATEGORY_LIST = 1077;

  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A3852
  US_STOCK_ORDER_STATUS_SCREEN = 1078;

  AUTH_LIVENESS_SUMMARY_STATUS_POLLSCREEN = 1079;

  IMAGE_CAPTURE_SCREEN = 1080;

  IMAGE_CAPTURED_PREVIEW_SCREEN = 1081;

  USER_DETAILS_FORM_SCREEN = 1082;

  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3314-44623&mode=design&t=3piCJeYsGoFu5YcP-4
  TIERING_EARNED_BENEFIT_SCREEN = 1083;

  // Screen to link phone email in LAMF
  // https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5674-30676&mode=design&t=9fvkFSsYXGLMsk2O-0
  // https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5679-31208&mode=design&t=9fvkFSsYXGLMsk2O-0
  LAMF_LINK_MF_SCREEN = 1084;

  // Bottom sheet to skip linking mutual funds in LAMF
  // https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5722-42359&mode=design&t=9fvkFSsYXGLMsk2O-0
  LAMF_SKIP_LINKING_MF_SCREEN = 1085;

  // Screen for entering account details for penny drop
  // https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4799-51956&mode=design&t=LlfJryFfDdzoIZRZ-4
  PENNY_DROP_ACCOUNT_DETAILS_SCREEN = 1086;

  // Screen for polling of  cardStatus
  // https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=5319-25810&mode=design&t=LlfJryFfDdzoIZRZ-4
  CARD_STATUS_POLL_SCREEN = 1087;

  // Screen for polling penny drop status
  // https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4799-52288&mode=design&t=LlfJryFfDdzoIZRZ-4
  PENNY_DROP_STATUS_POLL_SCREEN = 1088;

  // Screen for searching bank details using IFSC code
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=27124%3A47261&mode=dev
  BANK_INFO_SEARCH_SCREEN = 1089;

  // https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=16241-46903&mode=design&t=gijE8vQgnUDILbuZ-4
  LOANS_CALCULATION_BOTTOMSHEET = 1090;

  // Net Worth Refresh Screens
  // Screen for calling 'GetNextNetWorthRefreshAction' api with a loading screen
  NET_WORTH_REFRESH_GET_NEXT_ACTION = 1091;
  // Screen for showing bottom sheet when multiple instruments needs to be refreshed
  NET_WORTH_REFRESH_INIT_BOTTOM_SHEET = 1092;
  // Screen for showing form to update all manual assets
  NET_WORTH_REFRESH_MANUAL_ASSETS_REFRESH = 1093;
  // Screen for calling 'UpdateManualAssets' api to update all manual assets with a loading screen
  NET_WORTH_REFRESH_UPDATE_MANUAL_ASSETS = 1094;
  // Screen for showing success status after all assets are refreshed
  NET_WORTH_REFRESH_SUCCESS_SCREEN = 1095;

  // CONSENT SCREEN FOR CREDIT CARD LIMIT
  // Figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=40754-7114&mode=design&t=zI6b8qMpkpwCdeuS-4
  CC_LIMIT_CONSENT_SCREEN = 1096;

  // ACCEPT THE CHANGE IN CREDIT CARD LIMIT
  // Figma: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=41507-40335&mode=design&t=LKg87L0fIznK7RjT-4
  CC_ACCEPT_LIMIT_CHANGE_SCREEN = 1097;

  // Celebration Popup
  // Use case : 1. This is being used in tieiring earned benefits page when user opens the page for the first time on every month
  // https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-7332&mode=design&t=C8MVAZ1k7PtG8bnN-4
  CELEBRATION_POPUP = 1098;

  // earn benefits history
  // https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-6909&mode=design&t=adMrAaVS1582ag9m-4
  TIERING_EARNED_BENEFIT_HISTORY_SCREEN = 1099;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41416-154329&mode=design&t=zpA35OflGRRe5Hr0-4
  LOANS_DOCUMENTS_SCREEN = 1100;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52155&mode=design&t=fTYpRhjsruhHypBT-4
  LOANS_PREPAY_V2_SCREEN = 1101;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52183&mode=design&t=fTYpRhjsruhHypBT-4
  LOANS_REPAYMENT_METHODS_SCREEN = 1102;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-52243&mode=design&t=vBzUvqfnnSiSHxXC-4
  LOANS_PAY_VIA_CX_SCREEN = 1103;
  // Screen for authenticating user for updating email of given folios in lamf flow
  LAMF_EMAIL_UPDATE_NFT_USER_AUTH_OTP_SCREEN = 1104;
  // Screen for authenticating user for updating phone number of given folios in lamf flow
  LAMF_MOBILE_UPDATE_NFT_USER_AUTH_OTP_SCREEN = 1105;
  // Screen for verification of new email in updating email of folios in lamf flow
  LAMF_EMAIL_UPDATE_NFT_NEW_EMAIL_VERIFICATION_OTP_SCREEN = 1106;
  // Screen for verification of new mobile in updating mobile of folios in lamf flow
  LAMF_MOBILE_UPDATE_NFT_NEW_MOBILE_VERIFICATION_OTP_SCREEN = 1107;
  // Screen for linking user's mutual fund with primary phone and email
  LAMF_MF_LINK_SCREEN = 1108;
  // Failure screen when mf link fails to link a few of user's mutual fund.
  LAMF_MF_PARTIAL_LINK_FAILURE_SCREEN = 1109;
  // Failure screen when mf link fails to link any of user's mutual fund.
  LAMF_MF_LINK_FAILURE = 1110;
  // Reward Bottom Sheet Dialog in Txn Receipt Screen
  // Figma : https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=4240-12243&mode=design&t=czILGoiezN25iTOV-0
  TRANSACTION_REWARDS_SUMMARY_DIALOG = 1111;
  // https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=20262-17277&mode=design&t=PJsQK5cCDMKwaS6c-4
  // Deeplink for calling 'GetInvestmentSipsModificationScreen'
  GET_INVESTMENT_SIPS_MODIFICATION_SCREEN = 1112;

  VKYC_CALL_QUALITY_CHECK = 1113;

  LOANS_GENERIC_INFO_BOTTOM_SHEET = 1114;

  // [CX] Contact Us Landing Screen
  // Figma: https://www.figma.com/file/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?type=design&node-id=5816-13118&mode=design&t=jGUBCnhfvhQtXPcF-0
  CX_CONTACT_US_LANDING = 1115;
  // [CX] Screen where the user gets provided with options on how he will want to rersolve his issue, that includes a list of issue resolution suggestions and the option to call or chat with a customer executive
  // Figma: https://www.figma.com/file/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?type=design&node-id=5816-14543&mode=design&t=jGUBCnhfvhQtXPcF-0
  CX_CONTACT_US_TERMINAL = 1116;
  // [CX] Contact Us Issue Selection Bottom Sheet
  // shows up when pressing view all
  // Figma: https://www.figma.com/file/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?type=design&node-id=5816-14614&mode=design&t=OB5ogG50zaEqTSl1-0
  CX_CONTACT_US_ISSUE_SELECTION_BOTTOM_SHEET = 1117;
  // Figma: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=20267-18029&mode=design&t=MpXlnPMqEd5FnHKk-4
  UPDATE_STANDING_INSTRUCTION_PIN_SCREEN = 1118;
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=44338-40419&mode=design&t=CLBGpXyEbE6thRlm-4
  LOANS_MOBILE_NUMBER_INTRO_SCREEN = 1119;
  // figma link: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=14169-14092&mode=design&t=aiZJNa1FTxB04wvS-0
  LOANS_GENERIC_INTRO_SCREEN = 1120;
  LOANS_STATUS_POLL_SCREEN = 1121;
  // figma link: https://www.figma.com/file/qIVBwKJXJWRE7vaOU54kgs/%F0%9F%9B%A0%EF%B8%8F-Video-KYC-Workfile?type=design&node-id=18165-510666&mode=design&t=DaQGjHUg2kZaCNqi-0
  EPAN_INSTRUCTIONS_SCREEN = 1122;
  // Screen for taking lat long from the user
  // Figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/SimpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=6080-20230&mode=design&t=bjzJMZ3EIMa49ZKV-4
  MAP_ADDRESS_POINTER_SCREEN = 1123;
  // Screen for adding new address
  // this screen existed before but was not mapped to any deeplink
  // Figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/SimpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=6082-21119&mode=design&t=bjzJMZ3EIMa49ZKV-4
  ADD_NEW_ADDRESS_DETAILS_SCREEN = 1124;
  // Disclaimer before user proceeds with ENACH mandate cancellation
  // figma link: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=23674-48951&mode=design&t=aQRVrWH8CCWb7CsQ-0
  ENACH_CANCELLATION_DISCLAIMER_BOTTOM_SHEET = 1125;
  // Screen where user waits for some time before checking if linking of user's mutual fund was successful or not.
  LAMF_MF_INTERMEDIATE_LINK_SCREEN = 1126;

  VKYC_INSTRUCTIONS_WEB_VIEW_SCREEN = 1127;

  // Screen to show terminal state(either success/failure) for aa salary program flows like -
  // registration flows : income estimation from account check polls exhausted/ thus failure state | figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51726&mode=design&t=8hb8sMUkCPzdPEJq-0
  // verification flows : money transfer failure state | figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51292&mode=design&t=8hb8sMUkCPzdPEJq-0
  // activation flows : salary activated success terminal state | figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51258&mode=design&t=8hb8sMUkCPzdPEJq-0
  AA_SALARY_PROGRAM_FLOWS_TERMINAL_SCREEN = 1128;

  // AA_SALARY_PROGRAM_FLOWS_AMOUNT_TRANSFER_SETUP_SCREEN indicates the screen where user declares the amount of money to be transferred.
  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51447&mode=design&t=LCaSFfUaiubQKZSm-0
  AA_SALARY_PROGRAM_FLOWS_AMOUNT_TRANSFER_SETUP_SCREEN = 1129;

  // api to load landing page for aa salary flow
  // orchestrator api is called which decides on the screen to land user
  AA_SALARY_LANDING_SCREEN = 1130;

  // https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=21121-108057&mode=design&t=48uwAqezJBArRkbb-4
  // screen options: api/types/deeplink_screen_option/salaryprogram/aa_salary_flows_screen_options.proto
  AA_SALARY_ADD_FUNDS_VIA_OFF_APP_TRANSFER = 1131;

  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=29250-53141&mode=design&t=0yKDKXhDtylNwz2r-4
  // https://drive.google.com/file/d/16NBDLjcB_wNhTaZwjLadM2OyZfKcna7h/view?usp=drive_link
  // screen options: api/types/deeplink_screen_option/usstocks/screen_options.proto
  USSTOCKS_WALLET_BOTTOM_SHEET = 1132;

  // Screen to display a list/grid of user's opened rewards
  // https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=6478-18494&t=3KQ1F13a1uGPqXKW-0
  ALL_OPENED_REWARDS_LIST_SCREEN = 1133;

  LOANS_WEB_VIEW_WITH_POLL_SCREEN = 1134;

  // figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-64651&m=dev
  LOANS_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET = 1135;
  // Screen for showing loans offer details
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48296&t=fvrDnDMXVVtjw7ls-4
  LOANS_OFFER_DETAILS_SCREEN = 1136;

  // Screen to view all recent activities
  // refer : https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5727-12199&t=RQHbhg3cscJCSfYo-0
  HELP_RECENT_ACTIVITIES = 1137;

  // Screen to view details of a single recent activity
  // refer : https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5727-12519&t=RQHbhg3cscJCSfYo-0
  HELP_RECENT_ACTIVITY_DETAILS = 1138;

  // This screen refers to link mf screen where all folios are linked and portfolio fetch is required from user
  LAMF_MF_LINK_SUCCESSFUL_SCREEN = 1139;

  // Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=46393-26295&t=q6zuheZrcZJvN1GB-4
  LOANS_ALTERNATE_OFFER_SCREEN = 1140;

  // bottom sheet with options. On submit/continue the corresponding option deeplink is opened or any custom client handled api is called
  // https://www.figma.com/design/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=8517-10655&t=EdZ92LttpzP4EDW3-4
  REQUEST_CHOICE_SCROLLABLE_BOTTOM_SHEET = 1141;
  // https://www.figma.com/design/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=8517-10700&t=EdZ92LttpzP4EDW3-4
  INFO_ACKNOWLEDGEMENT_BOTTOM_SHEET = 1142;

  // Polling screen for liveness status
  LIVENESS_POLL_SCREEN = 1143;

  // asset landing page
  // figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7756-1401&t=Wj2bIQWtVkFUKYcg-4
  // drive: https://drive.google.com/file/d/1xVMUwkYN7u17hSh6OMn-4ihR6-E_YWKx/view?usp=drive_link
  ASSET_LANDING_PAGE = 1144;

  // https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=30205-28886&t=tNrEZaCh13KMfPZZ-0
  US_STOCKS_ACCOUNT_CREATION_INITIATED_SCREEN = 1145;

  // https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=30205-28907&t=tNrEZaCh13KMfPZZ-0
  US_STOCKS_ACCOUNT_CREATION_SUCCESS_SCREEN = 1146;

  // screen to check loan eligibility for a user. Client needs to call CheckEligibility rpc on navigating to this screen.
  LOANS_CHECK_ELIGIBILITY_SCREEN = 1147;

  // Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=18840-60250&t=0L1qYKhUCZdrPcKW-4
  // DC Dashboard v2
  DC_DASHBOARD_V2_SCREEN = 1148;

  // Figma: https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22372-2275&t=3Ipf2AmvgSrNChos-4
  // AA salary source of fund screen
  AA_SALARY_SOURCE_OF_FUND_SCREEN = 1149;

  // Figma: https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22401-79801&t=3OHqlyoyGx45FdbP-4
  // https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22453-83901&t=3OHqlyoyGx45FdbP-4
  // AA salary screen to pull salary data
  AA_SALARY_DATA_PULL = 1150;

  // Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=18840-58366&t=Fs7r5x36oykQDcVu-4
  // Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=18951-67112&t=Fs7r5x36oykQDcVu-4
  // Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=19486-9232&t=Fs7r5x36oykQDcVu-4
  // Fully SDUI driven bottom sheet
  SDUI_BOTTOM_SHEET = 1151;

  // Screen in the onboarding flow to allow users to choose among a variety of soft intents and personalise their app experience
  // https://www.figma.com/design/GNtKeutYAxPhAyBVvFxbMr/D2H-%E2%80%A2-Workfile?node-id=1778-22348&t=1LHThNz1BtxXaVKz-1
  ONBOARDING_SOFT_INTENT_SELECTION = 1152;

  // Figma: https://www.figma.com/design/kLX73V4pRucy8JM71ajH50/%F0%9F%9B%A0-US-stocks%2Fworkfile?node-id=7099-12536&t=yu4yZvNAbaubz3BB-4
  USS_FUNDS_TRANSFER_PROGRESS_SCREEN = 1153;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-49837&t=AfIAgEeR8jJ3Srx2-4
  // this will be used to fetch user current location in loan application journey
  LOAN_ADDRESS_VERIFICATION_INTRO_SCREEN = 1154;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-50114&t=9ZqzZkIVaGc6qZwD-4
  // this will be used to add the address of users, in this values will be auto populated based on user's current location or address selected
  // by the user from search results.
  LOAN_ADD_NEW_ADDRESS_DETAILS_SCREEN = 1155;

  // Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48233-51792&t=vsE6GEVpUMInkzdf-4
  LOANS_MULTIPLE_OFFER_DETAILS_SCREEN = 1156;
  // Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48233-51791&t=vsE6GEVpUMInkzdf-4
  LOANS_MULTIPLE_OFFER_SELECTION_SCREEN = 1157;
  // This screen will start vendor sdk(uqudo) to capture image and perform NFC scan of emirates id for nr onboarding.
  // https://docs.uqudo.com/docs
  INITIATE_UQUDO_SDK = 1158;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=48196-49912&t=L7KoARBVyyxuiEd7-4
  LOAN_ADDRESS_AUTOCOMPLETE_BOTTOMSHEET = 1159;
  // Secret Analyser Screen where we will show different analyser with charts and line items
  // Figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16174-29092&t=DPWf9k3dz2r3ATsl-1
  SECRET_ANALYSER_SCREEN = 1160;
  // Screen name for the in-house Video Kyc call screen:
  // https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=28192-26785&t=9Neg9Sf71mHz815M-4
  VKYC_CALL_SCREEN = 1161;
  // https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=305-13142&t=S9XAH2X60XUfyYdb-0
  IMAGE_CAPTURE_INFO = 1162;
  // https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=605-9795&t=S9XAH2X60XUfyYdb-0
  SUBMIT_DOC = 1163;
  // https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4020-15440&t=K2db9MekjF2YclS9-4
  JOURNEY_LANDING_SCREEN = 1164;
  //https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4076-18249&t=K2db9MekjF2YclS9-4
  JOURNEY_BOTTOMSHEET_SCREEN = 1165;
  // https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10088&t=tyjHNPf1L614K26I-0
  CONTACT_US_LANDING_SCREEN = 1166;
  // https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?t=tyjHNPf1L614K26I-0
  CONTACT_US_TERMINAL_SCREEN = 1167;
  // https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22964-120344&t=zusV9HPSk9xcBnFS-0
  SALARY_PROGRAM_HEALTH_INS_ONSURITY_INPUT_FORM = 1168;
  //https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=878-10032&t=jA3RH7SpOUor7O4p-4
  COUNTRY_SELECTION_BOTTOM_SHEET = 1169;
  // Figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16171-23989&t=51LZx7Z3lXC14GZP-4
  SECRET_ANALYSER_LIBRARY_SCREEN = 1170;
  // Below 3 screens allow user to re-select and submit the loan parameters (amount, tenure etc) during an application journey when the offer is revised
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48296&t=ineZGXSL5c6MuaPK-0
  // screen options: api.typesv2.deeplink_screen_option.preapprovedloans.RevisedLoanOfferDetailsScreenOptions
  REVISED_LOAN_OFFER_DETAILS_SCREEN = 1171;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48596&t=ineZGXSL5c6MuaPK-0
  // screen options: api.typesv2.deeplink_screen_option.preapprovedloans.RevisedLoanOfferCustomPlanSelectionBottomSheet
  REVISED_LOAN_OFFER_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET = 1172;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=45841-48955&t=ineZGXSL5c6MuaPK-0
  // screen options: api.typesv2.deeplink_screen_option.preapprovedloans.RevisedLoanOfferApplicationDetailsScreen
  REVISED_LOAN_OFFER_APPLICATION_DETAIL_SCREEN = 1173;
  // Figma : https://www.figma.com/design/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?node-id=5199-46670&t=6mKN7q19PSHVnJUk-4
  // api.typesv2.deeplink_screen_option.connectedaccount.BankSelectionScreenOptions
  CA_BANK_SELECTION = 1774;
  LOANS_AA_CONSENT_COLLECTION_SCREEN = 1775;
  // Figma : https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=27821-27806&t=tdmegSLjAwLHV3Zw-4
  // api.typesv2.deeplink_screen_option.consent.ConsentV2ScreenOptions
  CONSENT_V2 = 1776;
  // Figma : https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31238-5895&t=2rlztOighhcK6foN-4
  // api.typesv2.deeplink_screen_option.usstocks.screen_options_v2.USStocksCollectProofOfIdentitywww3wwawaawwwwwwwwwwwawaawaaaaaaaaaaaaaaAddressScreenOptions
  US_STOCKS_COLLECT_PROOF_OF_IDENTITY_ADDRESS_SCREEN = 1777;
  // Figma : https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31240-6611&t=YGf4IUuCjOEqo688-4
  // api.typesv2.deeplink_screen_option.usstocks.screen_options_v2.USStocksSubmitAdhaarScreenOptions
  US_STOCKS_SUBMIT_DIGILOCKER_AADHAAR_SCREEN = 1778;
  // Figma : https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31238-5966&t=YGf4IUuCjOEqo688-4
  // api.typesv2.deeplink_screen_option.usstocks.screen_options_v2.USStocksUploadPANBottomSheetOptions
  US_STOCKS_UPLOAD_PAN_BOTTOM_SHEET = 1779;
  // Figma: https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=27666-246065&t=yeyAUagvnOX7NYvB-0
  // api.typesv2.deeplink_screen_option.consent.ConsentV2ScreenOptions
  PERMISSIONS_BOTTOM_SHEET = 1780;
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14676&t=9qzkLma7WCLdgcSg-0
  // screen options: api.typesv2.deeplink_screen_option.contactus.ContactUsCategorySelectionScreenOptions
  CONTACT_US_CATEGORY_SELECTION_SCREEN = 1781;
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7946-7650&t=FsqgsDlfNkLSN4ch-0
  // screen options: api.typesv2.deeplink_screen_option.contactus.ContactUsCategorySelectionViewMoreScreenOptions
  CONTACT_US_CATEGORY_SELECTION_VIEW_MORE_BOTTOM_SHEET = 1782;
  // Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=21099-46723&t=QnDwNQavsfwU7LOI-4
  COUNTRY_SELECTION_BOTTOMSHEET = 1783;
  // Figma: https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=17094-20268&t=LKWOZBgpob0yTlNr-0
  SHORTCUT_OPTIONS_SCREEN = 1784;
  // Figma: https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=7451-23207&t=RyW3GGVkv8IGHTIc-0
  REWARDS_PROCESSING_SCREEN = 1785;
  // Figma: https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=13774-111183&t=V5y55zxOx6Ot6oq4-0?
  HOME_PROMPT_SCREEN = 1786;
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=2127-98327&t=qB410igv5ujGQoDY-1
  ADD_FUNDS_PROCESSING_SCREEN = 1787;
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=2127-98260&t=XzMdyGTd5njOutBA-1
  ADD_FUNDS_SUCCESS_SCREEN = 1788;
  // https://www.figma.com/design/3wbd6UmYwSqEpcER4lPvpf/AutoPay-%E2%80%A2-FFF?node-id=3820-15256&t=yZKTnDpVMrF5xVQh-1
  AUTOPAY_ARCHIVED_REQUESTS_SCREEN = 1789;
  // https://www.figma.com/design/pSoQn3AI6dC380ze9dzVHt/FFF-%E2%80%A2-Onboarding?node-id=16465-65326&t=v5nBcrcnwKtTKgnw-1
  ACCOUNT_CLOSURE_TRANSFER_INITIATED_SCREEN = 1790;
  // https://www.figma.com/design/mOAgJBEZ0dXhwJBR9HydJu/Account-Balances-%7C-Workfile?node-id=2797-47198&t=HTwkbbSIGUHviNgK-1
  BANK_TRANSFER_RECENT_PAYEES_SCREEN = 1791;
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=21952%3A22113&mode=dev
  LINK_CONNECTED_ACCOUNTS_VIA_TPAP_SCREEN = 1792;
  // https://www.figma.com/design/3wbd6UmYwSqEpcER4lPvpf/AutoPay-%E2%80%A2-FFF?node-id=3820-23753&t=6GZHVnzdb8GMXYtG-1
  CREATE_AUTOPAY_RULE_SCREEN = 1793;
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=22007%3A22096&mode=design&t=gRVVeqGRzWUA69jj-1
  PAY_LANDING_POPUP = 1794;
  PAYMENT_GATEWAY_CHECKOUT_WEB_VIEW = 1795;
  // Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=9484-44493&t=etEy0nrXd3fU5Rts-4
  DC_USAGE_AND_LIMIT_SETTINGS_SCREEN = 1796;
  // Onboarding screens names.....
  ONBOARDING_ERROR_SCREEN = 1797;
  CLOSE_ACCOUNT_REOPENING_PAN_VERIFICATION_SCREEN = 1798;
  ENTER_FINITE_CODE_V1 = 1799;
  PERMISSION_DENIED_SCREEN = 1800;
  SKIP_VKYC_BOTTOM_SHEET = 1801;
  VKYC_PRE_REQUISITE_SCREEN = 1802;
  SKIP_NOMINEE_BOTTOM_SHEET = 1803;
  ADD_GUARDIAN_SCREEN = 1804;
  INFO_ACKNOWLEDGEMENT_SCREEN = 1805;
  SHOW_CAMERA_MIC_PERMISSION_DENIED_SCREEN = 1806;
  HELPER_BOTTOM_SHEET = 1807;
  OFFERS_LANDING_V2 = 1808;
  // https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=31028-255234&node-type=frame&t=l8pqchLHIyfjpXCs-0
  PAN_VERIFICATION = 1809;
  // https://www.figma.com/design/xzcr5E8lsH1ihsh3RmqQTU/Pay-%E2%80%A2-FFF-%E2%80%A2--v1.3?node-id=13537-28514&t=YIexGKJoL4ItJhSP-1
  PAY_SEARCH_V2_SCREEN = 1810;

  // https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=10031-18074&t=1PdZC2oPrSdvoU11-4
  DC_ONBOARDING_INTRO = 1811;
  //https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9073&node-type=frame&t=3mz9KqdYwv0sjXia-0
  CATALOG_OFFER_REDEMPTION_BOTTOM_SHEET = 1812;
  // https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9225&t=RyOPmHuEw3RYqWZy-4
  REWARDS_ADDRESS_SELECTION_BOTTOM_SHEET = 1813;
  // https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9239&node-type=frame&t=ocYBnZsxF4kdCkhM-0
  // V2 for OFFERS_DETAILS
  OFFER_DETAILS_V2 = 1814;

  // Figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-17853&node-type=frame&t=mwzhgZXcWAcKun4o-0
  US_STOCKS_TRADE_DETAILS_SCREEN = 1815;

  // Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=63454-34056&t=WXHzMq429kA2DLtx-4
  LOANS_CONSENT_SCREEN = 1816;
  // Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11330-21495&t=eNzRYvO8zMq10N9E-4
  DC_TOGGLE_TRAVEL_MODE_CONFIRMATION_BOTTOM_SHEET = 1817;
  //https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=32155-36937&node-type=frame&t=xVRgzaBGcyrY2noY-0
  ONBOARDING_INTRO_V2_SCREEN = 1818;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=54513-23747&t=FX4qqjMEPyFyF9IU-4
  // This is Loans bottom sheet for single option selection(radio button list)
  LOANS_OPTION_SELECTION_BOTTOM_SHEET = 1819;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=54466-46642&t=cfiBff7SU9PUgvS7-4
  // This is Loans Full screen for single option selection(radio button list)
  LOANS_OPTION_SELECTION_FULL_SCREEN = 1820;
  // Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11906-21003&t=0bFEokg4ICMG3rUW-4
  DC_ORDER_PHYSICAL_CARD_V2_SCREEN = 1821;
  // figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11949-25101&t=0bFEokg4ICMG3rUW-4
  DELIVERY_ADDRESS_SELECTION_BOTTOM_SHEET = 1822;
  // figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=11938-21917&t=0bFEokg4ICMG3rUW-4
  DC_BENEFITS_DETAILS_BOTTOM_SHEET = 1823;
  // https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=33532-6586&node-type=frame&t=MOcE2hEkD00unWnW-0
  SMS_CONSENT = 1824;
  // figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=10031-19148&t=gd97SMDFHPNq2tmK-4
  DC_PHYSICAL_CARD_ORDER_SUCCESS_SCREEN_V2 = 1825;

  //https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=56140-24472&t=xlHnPFgaiXPDzKbb-4
  LOANS_OFFER_INTRO_SCREEN = 1826;
  // Figma: - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=56140-24674&t=iKj4ZPnj2ACVlVfs-4
  LOANS_SINGLE_VENDOR_MULTI_OFFER_SCREEN = 1827;
  //https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-39739&t=v83a5Lp6TOWOy1bL-1
  WEALTH_ANALYSER_REPORT_SCREEN = 1828;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=15220-20323&t=sl6llgU7Pd4RHsYY-4
  PL_INCOME_SELECTION_SCREEN = 1829;
  PAN_UPDATE_POLLING = 1830;
  // https://www.figma.com/design/5TZH6I0awKw8DMtj3aMzrC/Referrals-%E2%80%A2-FFF?node-id=12101-5707&node-type=frame&t=GnvcHDCXBvU0jawC-0
  CONTEXTUAL_CARD_BOTTOM_SHEET = 1831;
  ADD_FUNDS_ACK_BOTTOM_SHEET = 1832;
  PAN_DROP_OFF_BOTTOM_SHEET = 1833;
  INFO_BOTTOM_SHEET = 1834;
  DEFAULT_REFERRAL_CODE_APPLIED_DIALOG = 1835;
  EMPLOYER_SEARCH = 1836;
  CLEAR_EXISTING_DETAILS = 1837;
  DEFAULT_REFERRAL_CODE_DIALOG = 1838;
  KYC_CONSENT_INFO = 1839;
  PHONE_EMAIL_UPDATE_BOTTOM_SHEET = 1840;
  VIRTUAL_CARD_INFO_DIALOG = 1841;
  COMPANY_SEARCH_BOTTOM_SHEET = 1842;
  SPLASH = 1843;
  PERMISSION_RATIONALE = 1844;
  EKYC_EXPIRED_INFO_BOTTOM_SHEET = 1845;
  VKYC_DETAILED_BOTTOM_SHEET = 1846;
  SELECT_ADDRESS = 1847;
  GENERIC_BOTTOM_SHEET_INFO = 1848;
  EDIT_EMP_BOTTOM_SHEET_V2 = 1849;
  EDIT_ABSOLUTE_INCOME_V2 = 1850;
  EDIT_EMP_TYPE_V2 = 1851;
  EDIT_INCOME_V2 = 1852;
  EDIT_OCCUPATION_V2 = 1853;
  INFO_BOTTOM_SHEET_V2 = 1854;
  EMPLOYER_SEARCH_v2 = 1855;
  KYC_INFO = 1856;
  FI_LITE_INTRO_BOTTOM_SHEET = 1857;
  KYC_DETAIL_DIALOG = 1858;
  MANUAL_KYC = 1859;
  VKYC_CALL_SLOTS_BOTTOM_SHEET = 1860;
  VKYC_FEEDBACK_BOTTOM_SHEET = 1861;
  VKYC_LIMIT_WARNING = 1862;
  NOMINEE_ADDRESS_SELECTION = 1863;
  NOMINEE_ADDRESS = 1864;
  VKYC_ONB_STATUS = 1865;
  VKYC_NEXT_ACTION_API = 1866;

  // figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=14463-22745&t=xBd4KGpMQqgvzsJz-4
  ATM_LOCATOR_SCREEN = 1867;

  // This screen can be used for AA data Sharing.
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=55659-32858&t=IGqvyv0YRKv3kis1-4
  AA_DATA_SHARE_SCREEN = 1868;

  // Figma:- https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12584-6517&t=pZ5GzsPEOILv4z4z-4
  LAMF_REPAYMENT_METHODS_SCREEN = 1869;

  // Bottom sheet for country selection in DC international flow
  // https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=15011-47348&t=KgKRJvloBXHRHI4B-4
  DC_COUNTRY_SELECTION_BOTTOM_SHEET = 1870;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=1735-50761&t=UADT6C7pHwXIlSEM-4
  ASSET_DASHBOARD_SCREEN = 1871;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-10949&t=vbmOBUVASYRI3ZIN-4
  VERIFY_INCOME_HOME_SCREEN = 1872;

  // Tier loader screen
  // A lottie animation gets displayed when user tries to add fund or add funds to us stocks wallet or create FD/SD
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10193-125794&m=dev
  TIER_LOADER_SCREEN = 1873;
  // Tier all plans screen v2
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10002-54939&t=e3eOtSONnhAQGR5B-4
  TIER_ALL_PLANS_SCREEN_V2 = 1874;
  // Tier upgrade success screen v2
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10193-125818&m=dev
  TIER_UPGRADE_SUCCESS_SCREEN_V2 = 1875;
  // Loans know more screen - https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12584-6620&t=tA6ko9TFqnCRRkpg-4
  LOANS_KNOW_MORE_BOTTOM_SHEET = 1876;
  // Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-12038&t=0DJRrOF2NKL08GtL-0
  // Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-10853&t=0DJRrOF2NKL08GtL-0
  // Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61258-10889&t=0DJRrOF2NKL08GtL-0
  INCOME_ANALYSIS_STATUS_SCREEN = 1877;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=61577-41955&t=I2PzLlAbmpATOjGU-4
  SALARY_ACCOUNT_SELECTION_SCREEN = 1878;
  // Generic screen to be used for any flow completion, success or failure screen
  // e.g. Money secrets next screen after story completion
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=5580-46284&t=ZEWUctFYgsnUYce5-4
  GENERIC_FLOW_COMPLETION_SCREEN = 1879;
  // Screen that collect old passport file number or ARN number of the passports that is issued outside india for NR onboarding
  // https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=3183-10195&t=Fa7AeskhVRGFWPRI-4
  GLOBAL_ISSUED_PASSPORT_DATA_VERIFICATION = 1880;
  // Tier drop off bottom sheet
  // Bottom sheet which gets displayed when user clicks back from add funds or us stocks or create FD/SD
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10277-72545&t=mXS9zsieZAJj8r2h-4
  TIER_DROP_OFF_BOTTOM_SHEET = 1881;

  // VKYC generic bottom sheet driven by SDUI, this is use as vkyc drop off bottom sheet and showing some generic info
  // https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-37053&t=lTeXolgSjyKy1n0S-4
  // https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1212-33521&t=lTeXolgSjyKy1n0S-4
  VKYC_GENERIC_SDUI_BOTTOMSHEET = 1882;
  // Version 2 of the Vkyc intro screen, driven by SDUI:
  // https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-34489&t=EreivkjdPffOwwix-4
  VKYC_INTRO_V2_SCREEN = 1883;
  // Vkyc steps info screen, driven by SDUI:
  // https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-34617&t=EreivkjdPffOwwix-4
  VKYC_STEPS_INFO_SCREEN = 1884;
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10175-83805&t=f2oABQDtEgTdOtwz-4
  // deeplink for tiering landing screen - rpc will be called to get the screen options to construct the screen
  // screen options: TieringLandingScreenOptions
  // rpc called: GetTierFlowScreen
  TIERING_LANDING_SCREEN = 1885;
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10175-83805&t=f2oABQDtEgTdOtwz-4
  // deeplink for tiering drop off full screen - screen options will be passed to construct the screen
  // screen options: DropOffFullScreenOptions
  TIERING_DROP_OFF_FULL_SCREEN = 1886;
  // Tier detailed benefits bottom sheet
  // Bottom sheet which gets displayed when user clicks on view details of any tier
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10043-11580&t=t76RUnvyrif5rq8v-4
  TIER_DETAILED_BENEFITS_BOTTOM_SHEET = 1887;
  // deeplink for wealth builder landing screen
  WEALTH_BUILDER_LANDING_SCREEN = 1888;
  // Figma - https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=9255-54117&t=t2K4QJevtLdS8EZ1-4
  // Screen for selecting a bank account Auto Verification
  LOANS_AUTO_PAY_AUTH_METHOD_SELECTION_BOTTOM_SHEET = 1889;
  // https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=121-9682&t=TMDdzjxiprl4u9IS-4
  ASSET_IMPORT_STATUS_POLLING_SCREEN = 1890;
  // Polling screen to get the next action after the physical card dispatch.
  GET_PHYSICAL_CARD_DISPATCH_NEXT_ACTION_SCREEN = 1891;
  // Figma - https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=9295-39322&t=cpkfGPIy5hMHmKhH-4
  LOAN_APPLICATION_ERROR_STATUS_SCREEN = 1892;
  // PAN prefill and mutual fund import consent screen for the wealth builder 2.0 flow
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=328-16776&t=ftAnK2nwPrGYwISh-0
  MF_HOLDINGS_IMPORT_PAN_CONSENT_SCREEN = 1893;
  // Success Screen for updating nominee details for savings account
  // Figma: https://www.figma.com/design/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=14542-41244&t=UabtNsT83emYz239-4
  SA_NOMINEE_DETAILS_UPDATE_SUCCESS_SCREEN = 1895;
  // Screen to show home walkthrough to user
  // https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-60286&p=f&t=UbesNP58UgvnGejK-0
  WALKTHROUGH_SCREEN = 1896;
  // Screen for calling SendSmsData rpc while showing a loading screen
  // https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=413-14023&t=QnqXdSa3Zalejzao-4
  SEND_SMS_DATA = 1897;
  // Screen to show connect more assets on wealth builder landing page
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7351-45880&t=hw262jvGNUlVPeSO-4
  WEALTH_BUILDER_LANDING_CONNECT_MORE_SCREEN = 1898;
  // Screen to show Portfolio tracker's landing page
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1566&t=Q9eQjZjJg10nxoIA-4
  PORTFOLIO_TRACKER_LANDING_SCREEN = 1899;
  // Screen to show Portfolio tracker's Asset details page
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3721-20157&t=Q9eQjZjJg10nxoIA-4
  PORTFOLIO_TRACKER_ASSET_DETAILS_SCREEN = 1900;
  // Figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-64946&t=IGmJANLtILqYrUaN-4
  LOANS_FORM_ENTRY_SCREEN = 1901;
  // represents credit card dashboard screen v2
  // https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5716&t=NpQCwd13A6FBIgnw-4
  CREDIT_CARD_DASHBOARD_SCREEN_V2 = 1902;
  // In Loans, whenever we need to take the user's confirmation for any action, we can use this bottom sheet
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=54513-23027&t=kg9M6hgq9KNlUf7x-0
  LOANS_CONFIRMATION_BOTTOM_SHEET = 1903;
  // Success Screen for reward claim flow
  // Currently will only be used for Fi-Coins flow.
  // https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=8073-19980&t=MY1jWYlSz8xJsL1Z-4
  REWARD_CLAIM_SUCCESS_SCREEN = 1904;
  // Screen to render the Credit Cards SDK.
  // https://www.figma.com/design/tbtICUWo4zqseZBUUzPZOl/CC-ONB-flow-for-Saven?node-id=192-35987&t=Q1WqNlTRVHd7taxn-4
  CREDIT_CARD_SDK_SCREEN = 1905;
  // Figma - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=69553-38807&t=xKsnIo792toAzLeX-4
  LOANS_CONSENT_V2_SCREEN = 1906;
  // Figma - https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay---Workfile?node-id=36676-31931&t=DHrVlfHubQnxWK4A-0
  // Screen to show the Average Monthly Balance details for the user
  AMB_DETAILS_SCREEN = 1907;
  CREDIT_REPORT_POLL_STATUS = 1908;
  CREDIT_REPORT_CONSENT_V2 = 1909;
  RECORD_AUTH_FLOW_COMPLETION = 1910;
  INITIATE_CREDIT_REPORT_DOWNLOAD_FOR_ANALYSER = 1911;
  // Figma - https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35748&t=4SzjTwXgOKZ9sTE9-4
  // New Contact Us Landing screen for the user to resolve any issues faced in the app.
  // This is the V2 of CONTACT_US_LANDING_SCREEN.
  // Example: User clicks on "Contact Us" and lands on this screen which shows options like "Recent Issues", "FAQs", etc.
  CONTACT_US_LANDING_SCREEN_V2 = 1912;
  // Screen to display a list of open/active incidents or service disruptions.
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35931&t=4SzjTwXgOKZ9sTE9-4
  OPEN_INCIDENTS_SCREEN = 1913;

  // Deeplink to navigate to the Search Query Screen
  // Figma: https://www.figma.com/design/HqMQEG4wBLPvhjdq1VEj3j/%E2%98%8E%EF%B8%8F-CX-workfile?node-id=70-187&t=Qrg5D7UsFKIkXMc9-0
  CONTACT_US_QUERY_SCREEN = 1914;

  // Figma: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=74513-37832&t=JHfSdzSbmlwPJwbn-0
  LOANS_PERMISSION_BOTTOM_SHEET_SCREEN = 1915;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=74513-37325&t=KW7tl7yDgNqbY4lQ-0
  LOANS_APPLICATION_CONFIRMATION_BOTTOM_SHEET = 1916;
  // Figma - https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2391-259&t=B4JCaNn35YGRKPdq-4
  CC_INTRO_V2_SCREEN = 1917;

  GENERIC_PRELAUNCH_SCREEN = 1918;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=75523-46825&t=nxmxSGLBM1kmeBal-4
  LOANS_KEY_VALUE_ROWS_BOTTOM_SHEET = 1919;

  // Screen to show the magic import screen for networth which displays scan and review screens
  // Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15699&t=Iy3TPAyrAFPn1jPi-4
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15728&t=Iy3TPAyrAFPn1jPi-4
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15732&t=Iy3TPAyrAFPn1jPi-4
  NETWORTH_MAGIC_IMPORT_SCREEN = 1920;

  // Figma : https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=3107-12167&t=tpdZmbOYu5cVusPo-0
  // api.typesv2.deeplink_screen_option.consent.SaDeclarationScreenOptions
  SA_DECLARATION = 1921;
  // Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=13604-13207&t=Gyk1mAcTx6b3G9sr-4
  // Screen options: api.typesv2/deeplink_screen_options.assetandanalysis.WealthGenericSduiScreenOptions
  WEALTH_SDUI_BOTTOM_SHEET = 1922;

  // Figma - https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=3007-12881&t=y7Jv1lCWlM1R2xOV-0
  CONFIRM_CARD_MAILING_ADDRESS_SCREEN_V2 = 1923;
  // Digilocker Intro Screen
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=64895-29596&t=FEPmn6AdR7Urh3qU-1
  WEB_DIGILOCKER_INTRO_SCREEN = 1924;
  // SG KYC Multi option screen
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=64895-29655&t=e072nNSo04USyk3D-4
  WEB_STOCKGUARDIAN_KYC_CHOICE_SCREEN = 1925;
  // SG KYC Retry screen
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=66085-9215&m=dev
  WEB_KYC_RETRY_SCREEN = 1926;
  // used to confirm KYC application using OTP
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=76543-95399&m=dev
  WEB_KYC_CONFIRMATION_VIA_OTP_SCREEN = 1927;
  // https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=4131-2696&t=CQ4BU3IWZxj822Ih-4
  EARNED_REWARDS_HISTORY_SCREEN = 1928;
  // https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2262-4544&t=CQ4BU3IWZxj822Ih-4
  CC_DETAILS_AND_BENEFITS_SCREEN = 1929;
  // https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-96670&t=XhP8BlFamoV8q77J-4
  RECHARGE_INTRO_SCREEN = 1930;
  // https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-97597&t=XhP8BlFamoV8q77J-4
  RECHARGE_PLANS_SCREEN = 1931;
  // https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-99237&t=XhP8BlFamoV8q77J-4
  BILL_DETAILS_CONFIRMATION_SCREEN = 1932;
  // screen for cancellation option bottom sheet to be used in salary estimation service
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=77530-89679&t=QYfqQ7fleiJx5xaF-4
  SALARY_EST_CANCELLATION_BOTTOM_SHEET = 1933;
  // Collect csat for latest resolved ticket (if any)
  COLLECT_CSAT_SURVEY = 1934;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=16048-23163&t=qmTzSq6nX4YOgW1k-0
  NETWORTH_MAGIC_IMPORT_IDEAS_SCREEN = 1944;

  // screen to display list of consents, this will be scrollable page
  // https://www.figma.com/design/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=21053-3867&t=sIV9m0wWxgcuqPsq-4
  // https://drive.google.com/file/d/13VeL_lDoKH8BcyB4oNXu9uzoIJjvusOV/view?usp=sharing
  WEALTH_GENERIC_RECORD_CONSENT = 1945;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=77728-356593&p=f&t=BHedV8jy6vEfO0hS-0
  LOANS_DASHBOARD_V4_SCREEN = 1946;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=78102-98537&t=ABpAbjLBZ1hncmaO-4
  LOANS_SUMMARY_SCREEN = 1947;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-FFF--Wealth-Builder?node-id=17842-22659&t=uuMtslO6scQPROQo-0
  INTERACTIVE_TALK_TO_AI_SCREEN = 1948;

  // CKYC otp verification bottomsheet
  // https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/%F0%9F%9B%A0%EF%B8%8F-New-Onboarding-Workfile?node-id=5502-5276&t=EvwGo5EYQnA4wNFQ-0
  VERIFY_CKYC_OTP_BOTTOMSHEET = 1949;
  // Skip CKYC otp verification bottomsheet
  // https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/%F0%9F%9B%A0%EF%B8%8F-New-Onboarding-Workfile?node-id=5502-7172&t=EvwGo5EYQnA4wNFQ-0
  SKIP_VERIFY_CKYC_OTP_BOTTOMSHEET = 1950;
}
