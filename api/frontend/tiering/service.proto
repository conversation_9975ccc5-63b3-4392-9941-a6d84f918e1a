// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package frontend.tiering;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/tiering/earned_benefits_layout.proto";
import "api/frontend/tiering/enum/enum.proto";
import "api/frontend/tiering/tiering_layout.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/tiering/screen_options.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "google/protobuf/duration.proto";
import "api/typesv2/ui/sdui/properties/visual_properties.proto";

option go_package = "github.com/epifi/gamma/api/frontend/tiering";
option java_package = "com.github.epifi.gamma.api.frontend.tiering";

service Tiering {
  // Service to get tiering launch parameters when the app starts
  // Returns deeplink to go to next available screen
  // In case tiering is not enabled for a user yet flag will be set to false and rpc status code will be OK
  // INTERNAL in case of any server error
  rpc GetTieringLaunchInfo (GetTieringLaunchInfoRequest) returns (GetTieringLaunchInfoResponse) {};

  // this rpc will be used to get deeplink by screen
  // it will be used as a fallback for deeplink when screen options for the corresponding screen is not present
  rpc GetDeeplink (GetDeeplinkRequest) returns (GetDeeplinkResponse) {}

  // RPC to upgrade manually to a tier in case user was eligible
  // In case RPC throws any error other than status code "OK" client should fallback to
  // error details sent in screen options for TIER_MANUAL_UPGRADE deeplink
  rpc Upgrade (UpgradeRequest) returns (UpgradeResponse) {};

  // RPC to fetch all tier plans based on UI context. Backend will do default handling for nil UI context.
  rpc GetTierAllPlans (GetTierAllPlansRequest) returns (GetTierAllPlansResponse) {}

  // RPC to record when a particular display component has been shown to the actor
  // Client calls this once a particular screen/component has been shown to a user
  rpc RecordComponentShownToActor (RecordComponentShownToActorRequest) returns (RecordComponentShownToActorResponse) {}

  // RPC to fetch earn benefit of the user.
  rpc GetTierEarnedBenefits (GetTierEarnedBenefitsRequest) returns (GetTierEarnedBenefitsResponse);

  // RPC to get earn benefit history
  rpc GetEarnedBenefitsHistory (GetEarnedBenefitsHistoryRequest) returns (GetEarnedBenefitsHistoryResponse);

  // RPC to determine where to land user next could be add funds, us stocks or any other.
  rpc GetTierFlowScreen (GetTierFlowScreenRequest) returns (GetTierFlowScreenResponse);

  // RPC to return tier all plans v2 screen options
  rpc GetTierAllPlansV2 (GetTierAllPlansV2Request) returns (GetTierAllPlansV2Response);

  rpc GetDetailedBenefitsBottomSheet (GetDetailedBenefitsBottomSheetRequest) returns (GetDetailedBenefitsBottomSheetResponse);

  // GetAMBScreenDetails is used to fetch the AMB details for the user
  rpc GetAMBScreenDetails (GetAMBScreenDetailsRequest) returns (GetAMBScreenDetailsResponse);
}

message GetDetailedBenefitsBottomSheetRequest {
  frontend.header.RequestHeader req = 1;
  // meta data for the request
  string meta_data = 2;
}

message GetDetailedBenefitsBottomSheetResponse {
  frontend.header.ResponseHeader resp_header = 1;

  api.typesv2.ui.sdui.sections.Section section = 2;
  // Next action cta
  deeplink.Cta cta = 3;
  // Contains tier plan-related metadata for analytics events
  map<string, string> tier_plan_meta_map = 4;
}

message RecordComponentShownToActorRequest {
  frontend.header.RequestHeader req = 1;
  // Component name to be sent in request by client whenever it is displayed to user (mandatory)
  frontend.tiering.enum.DisplayComponent component_name = 2;

}

message GetEarnedBenefitsHistoryRequest {
  frontend.header.RequestHeader req = 1;
  // after token is to be passed if client wants to fetch activities that happened after previous page.
  // in this case the events will be ordered in ASCENDING order of activity time.
  string token = 2;
}

message GetEarnedBenefitsHistoryResponse {
  // Response header
  frontend.header.ResponseHeader resp_header = 1;
  message HeaderView {
    // Ex: [ICON] - plus tier
    api.typesv2.ui.IconTextComponent plan_icon = 1;
    // Ex: TOTAL BENEFITS EARNED
    api.typesv2.common.Text total_benefits_earned = 2;
    // EX: Amount ₹5,000
    api.typesv2.common.Text total_benefits_earned_value = 3;
    // EX: How is this calculated?
    api.typesv2.ui.IconTextComponent bottom_text = 4;
  }

  // Plus lifetime benefits
  api.typesv2.common.Text page_title = 2;

  HeaderView header_view = 3;

  repeated MonthlyRewardEarnedView monthly_reward_earned_views = 4;
  // view more cta
  api.typesv2.ui.IconTextComponent view_more_cta = 5;

  // before_token to be used when client wants to fetch lifetime benefit that happened before the current page.
  string before_token = 6;

  // after_token to be used when client wants to fetch lifetime benefit that happened after the current page.
  string after_token = 7;

  // Banner notification that appears at the top of the screen to display important announcements or updates
  // The banner can include an icon, text, and an optional call-to-action with deeplink
  api.typesv2.ui.IconTextComponent header_banner = 8;
}

message MonthlyRewardEarnedView {
  message HeaderView {
    // Ex: Background color for header
    api.typesv2.common.ui.widget.BackgroundColour background_colour = 1;
    //Ex: December 2023
    api.typesv2.common.Text title = 2;
    // EX: ONGOING
    api.typesv2.ui.IconTextComponent tag = 3;
  }

  message BenefitCardItem {
    // Ex: Fi-COINS EARNED
    api.typesv2.common.Text title = 1;
    // Ex: ₹899
    api.typesv2.ui.IconTextComponent value = 2;
  }

  HeaderView header_view = 1;
  api.typesv2.common.ui.widget.BackgroundColour card_background_colour = 2;
  // EX: BENEFITS EARNED
  api.typesv2.common.Text total_benefits_earned = 3;
  // EX: ₹1,000
  api.typesv2.common.Text total_benefits_earned_value = 4;
  api.typesv2.common.ui.widget.BackgroundColour benefit_background_colour = 5;
  repeated BenefitCardItem benefit_card_items = 6;
}

message RecordComponentShownToActorResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message GetTieringLaunchInfoRequest {
  // common request header for frontend rpc's
  frontend.header.RequestHeader req = 15;
}

message GetTieringLaunchInfoResponse {
  // response header common across frontend rpc's
  // this has the status and error view
  frontend.header.ResponseHeader resp_header = 15;
  // Flag to denote whether tiering as a feature is enabled/disabled
  // In case tiering is disabled client will not show anything related to tiering to the user
  api.typesv2.common.BooleanEnum is_tiering_enabled = 1;
  // Play the animation only after an inactivity of these many seconds
  int32 launch_animation_inactivity_seconds = 2;
  // tier introduction deeplink with launch animation lottie file
  frontend.deeplink.Deeplink tier_introduction_deeplink = 3;
  // To enable disable animation from backend
  api.typesv2.common.BooleanEnum is_launch_animation_enabled = 4;
  // identifier will be used by client to persist this and have logic regarding showing tier intro
  // Will be a plain string passed
  string identifier = 5;
}

message GetDeeplinkRequest {
  frontend.header.RequestHeader req = 1;
  // screen for which deeplink need to be shared
  deeplink.Screen screen = 2;
}

message GetDeeplinkResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink for the requested screen
  // this must contain the screen options of the corresponding link screen if any
  deeplink.Deeplink deeplink = 2;
}

message UpgradeRequest {
  frontend.header.RequestHeader req = 1;
  // tiering.enums.Provenance.String()
  // optional parameter
  // if empty, we will use PROVENANCE_FREE_UPGRADE as default provenance
  string provenance = 2;
}

message UpgradeResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink deeplink = 2;
}

message GetTierAllPlansRequest {
  frontend.header.RequestHeader req = 1;
  // UI context from where the user came to the flow
  string ui_context = 2;
}

message GetTierAllPlansResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink for TIER_ALL_PLANS
  deeplink.Deeplink tier_all_plan = 2;
}

message GetTierEarnedBenefitsRequest {
  frontend.header.RequestHeader req = 1;
  // It will have a list of all string of benefit type that need to be refreshed.
  // will be empty to fetch all the components
  // benefit type:
  // - BENEFITS_TYPE_DEBIT_CARD : for retrying debit card benefits
  // - BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW : for retrying upgrade benefit
  // - BENEFITS_TYPE_MORE_INFO_VIEW :- for retrying more info view benefits
  // - BENEFITS_TYPE_MONTHLY_VIEW :- for retrying monthly view benefits
  // - BENEFITS_TYPE_OTHER :- for retrying other benefits
  repeated string benefit_type = 2;
}

message GetTierEarnedBenefitsResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // Internal error while processing the request
    INTERNAL = 13;
    // status code when fetching of any benefit failed
    // the screen will load for this status code with partial data
    FETCH_FAILED = 101;
    // status code when screen is not enabled for the tier user is in
    SCREEN_UNAVAILABLE_FOR_TIER = 102;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // screen options to load header and shimmer
  api.typesv2.deeplink_screen_option.tiering.EarnedBenefitScreenOptions screen_options = 2;
  // total benefit title view
  // ex: TOTAL BENEFITS EARNED
  api.typesv2.ui.IconTextComponent total_benefit_earned = 3;
  // Value of total benefit
  // ex: [Icon] totalBenefitAmount [Icon]
  api.typesv2.ui.IconTextComponent total_benefit_earned_value = 4;
  //Optional:- Warning banner
  WarningView warning_counter_view = 5 [deprecated = true];
  // Total Reward earned by user in a month
  MonthlyBenefitView monthly_benefit_view = 6 [deprecated = true];
  // List of all reward option on the page
  repeated BenefitsOptions benefit_options = 7;
  // Users current tier for events purpose.
  string tier_identifier = 8;
  // no popup is shown if celebration_popup is nil
  // https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-7332&mode=design&t=C8MVAZ1k7PtG8bnN-4
  deeplink.Deeplink celebration_popup = 9;
  // duration for which pop up will not be shown after user dismisses the popup
  // default will be 0 and user will see the popup every time
  google.protobuf.Duration celebration_popup_cooloff_duration = 10;
  // cache key for the popup. used for persisting whether to show the celebration popup
  string popup_cache_key = 11;
}

message BenefitsOptions {
  // Top title for the view
  api.typesv2.common.Text title = 1;
  // corner radius for benefits view
  uint32 corner_radius = 2;
  // renders the component in greyscale
  bool should_grayscale = 3;
  // backgroundColour for benefits view
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 4;
  // benefit type: frontend.tiering.BenefitsType.String()
  // - BENEFITS_TYPE_DEBIT_CARD
  // - BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW
  // - BENEFITS_TYPE_MORE_INFO_VIEW
  // - BENEFITS_TYPE_OTHER
  string benefit_type = 5;
  // the benefit option with higher priority is shown at the top of the screen
  // [used by backend for ordering the list of benefit options before sending it to client]
  uint32 priority = 13;

  oneof option {
    // List of benefits with zero / non - zero stats
    // Figma:- https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3527-112531&mode=design&t=gh3sCRk4mMBlAMvK-4
    // Figma:- https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3527-112490&mode=design&t=<gh3sCRk4mMBlAMvK-4> </gh3sCRk4mMBlAMvK-4>
    // Figma : https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-8462&mode=design&t=8UxlG4zMJ5MiBaO4-4
    // Figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20975-50543&mode=design&t=hUIpI3dWiLLsDMfm-4
    ActiveStateListView active_state_list_view = 6;
    InActiveStateListView in_active_state_list_view = 7;
    UpgradeBenefitsView upgrade_benefits = 8;
    MoreInfoView more_benefits = 9;
    RetryView retry_view = 10;
    TransferSalaryView transfer_salary_view = 11;
    MonthlyBenefitView monthly_benefit_view = 12;
    WarningView warning_view = 14;
    RewardView reward_view = 15;
  }
}

// Figma: https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10218-144968&t=TjMOuYP2DfC6NzfV-4
message TransferSalaryView {
  TitleView header_view = 1;
  api.typesv2.common.Text amount = 2 [deprecated = true];
  api.typesv2.ui.IconTextComponent edit_amount = 3;
  api.typesv2.ui.IconTextComponent right_cta = 4;
  api.typesv2.ui.IconTextComponent info_view = 5;
  uint32 corner_radius = 6;
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 7;
  api.typesv2.ui.IconTextComponent amount_icon_text_component = 8;
}

// Figma:-  https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-8462&mode=design&t=8UxlG4zMJ5MiBaO4-4
message UpgradeBenefitsView {
  message Card {
    // ex: RECOMMENDED
    api.typesv2.ui.IconTextComponent tag = 1;
    // icon
    api.typesv2.common.VisualElement visual_element = 2;
    // ex: 8000
    api.typesv2.ui.IconTextComponent cashback = 3;
    // ex: Fi-Coins
    api.typesv2.ui.IconTextComponent coins = 4;
    // background color for complete card
    api.typesv2.common.ui.widget.BackgroundColour background_colour = 5;
    // corner radius
    int32 corner_radius = 6;
    // Tier identifier for events and testing purpose
    string tier_identifier = 7;
  }
  // Same transactions, higher rewards
  api.typesv2.ui.IconTextComponent component_title = 1;
  // Left card
  Card left_card = 2;
  // Right card
  Card right_card = 3;
  // VS text
  api.typesv2.ui.IconTextComponent vs_itc = 4;
  // Upgrade now
  api.typesv2.ui.IconTextComponent cta = 5;
  // Higher tier user want to upgrade to.
  string tier_identifier = 8;
}

// Figma:- https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3314-44066&mode=design&t=gh3sCRk4mMBlAMvK-4
message MoreInfoView {
  // List of more benefits for CTA
  repeated api.typesv2.ui.IconTextComponent icon_text_components = 1;
}

// Figma: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3314-44385&mode=design&t=B7odRS6EUTFnulAi-4
message RetryView {
  // [icon]
  api.typesv2.common.VisualElement icon = 1;
  // Retry
  api.typesv2.common.Text title = 2;
  // Retry cta [No deeplink expected]
  api.typesv2.ui.IconTextComponent cta = 3;
}

message TitleView {
  // Month (ex: January rewards)
  api.typesv2.common.Text title = 1;
  // Title background color
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 2;
  // Right info icon
  // on tap it should move to info deeplink
  api.typesv2.ui.IconTextComponent right_icon = 3;
}

// Figma https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1221-24019&mode=design&t=MH9sve3mUacfqMPW-4
// https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1221-24275&mode=design&t=MH9sve3mUacfqMPW-4
message MonthlyBenefitView {
  message MonthlyBenefits {
    // Title view for month view
    message TitleView {
      option deprecated = true;
      // Month (ex: January rewards)
      api.typesv2.common.Text title = 1;
      // Title background color
      api.typesv2.common.ui.widget.BackgroundColour background_colour = 2;
      // Right info icon
      // on tap it should move to info deeplink
      api.typesv2.ui.IconTextComponent right_icon = 3;
    }

    message MonthlyBenefitTile {
      // ex: fi coin icon / Cashback icon
      api.typesv2.common.VisualElement left_icon = 1 [deprecated = true];
      // Example : FI-COINS EARNED (2X)
      api.typesv2.common.Text title = 2 [deprecated = true];
      // Divider view $earned amount/$ potential total amount
      DividerView divider_view = 3 [deprecated = true];
      // [0-100]
      uint32 progress_percentage = 4;
      // color for progress bar active state
      string progress_active_color = 5;
      // color for progress bar background
      string progress_background_color = 6;
      // E.g., Credited to account on 5th of every month
      // Monthly limit reached
      api.typesv2.ui.IconTextComponent subtitle = 7 [deprecated = true];
      // ex: 3% back on all transactions / (icon) Monthly limit reached
      api.typesv2.ui.IconTextComponent title_itc = 8;
      // Plant icon
      api.typesv2.common.VisualElement plant_icon = 9;
      // List of earning view to show values in rotating manner
      // Note : If the list contains less than 2 items, then there won't be any rotating animation.
      repeated EarningView earning_views = 10;
      // No of times to rotate the divider view and display 1st element after that.
      int32 no_of_rotations = 11;


      // ex ₹0 / ₹5,000
      message DividerView {
        option deprecated = true;
        // ex ₹0
        api.typesv2.ui.IconTextComponent numerator = 1;
        // ex: /
        api.typesv2.common.Text divider = 2;
        // ex: ₹5,000
        api.typesv2.ui.IconTextComponent denominator = 3;
      }

      message EarningView {
        repeated api.typesv2.ui.IconTextComponent items = 1;
        // Content color taken as background color as need to add support for gradient
        // Will be applied to each of the text in the items
        api.typesv2.common.ui.widget.BackgroundColour content_color = 2;
        // The content will be visible for given time
        int32 visible_millis = 3;
      }
    }
    // header view
    TitleView header_view = 1 [deprecated = true];
    // list of benefit earn by user
    repeated MonthlyBenefitTile monthly_benefit_tiles = 2;
    // bottom info title
    api.typesv2.ui.IconTextComponent info_view = 3 [deprecated = true];
    // corner radius for complete view
    uint32 corner_radius = 4;
    // background color for complete view
    api.typesv2.common.ui.widget.BackgroundColour background_colour = 5;
    // renders the component in greyscale
    bool should_grayscale = 6;
    // header view
    tiering.TitleView header_view_v2 = 7;

    message OfferSection {
      api.typesv2.ui.IconTextComponent title = 1;
      oneof offer_content {
        api.typesv2.common.VisualElement visual_element = 2;
        OfferView offer_view = 3;
        api.typesv2.ui.sdui.sections.Section generic_sdui_section = 4;
      }
    }
    OfferSection offer_section = 8;

    api.typesv2.ui.IconTextComponent amb_entry_point = 9;
  }
  MonthlyBenefits monthly_benefits = 1;
  // benefit type: (value can only be `BENEFITS_TYPE_MONTHLY_VIEW`)
  // - BENEFITS_TYPE_MONTHLY_VIEW
  string benefit_type = 2;
}

message WarningView {
  // Figma :- https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3314-44909&mode=design&t=gh3sCRk4mMBlAMvK-4
  message WarningCounterView {
    // Background color for the view
    api.typesv2.common.ui.widget.BackgroundColour background_colour = 1;
    // Title for the Counter view (ex: 5)
    api.typesv2.common.Text title = 2;
    // Subtitle align vertically for the container view (ex: DAYS LEFT)
    api.typesv2.common.Text sub_title = 3;
  }
  oneof left_view {
    WarningCounterView warning_counter_view = 1;
    // ex warning icon
    api.typesv2.common.VisualElement visual_element = 2;
  }
  // Title for the view
  // Example :- Plus Plan expires soon! Add ₹8,000 to continue enjoying benefits.
  api.typesv2.common.Text title = 3;
  // subtitle for the warning view
  api.typesv2.common.Text subtitle = 4;
  // Right icon for component (ex: chevron icon)
  api.typesv2.common.VisualElement right_icon = 5;
  // Background color for whole component
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 6;
  // Example: 20px
  uint32 corner_radius = 7;
  // to navigate on click of warning view
  deeplink.Deeplink deeplink = 8;
  // right text component
  api.typesv2.ui.IconTextComponent right_text_component = 9;
}

// Figma: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3527-112385&mode=design&t=gh3sCRk4mMBlAMvK-4
message InActiveStateListView {
  message ContainerView {
    // background color for active container
    api.typesv2.common.ui.widget.BackgroundColour background_colour = 1;
    repeated BenefitList benefit_lists = 2;
    uint32 corner_radius = 3;
    // list of all benefit for active state
    message BenefitList {
      // ex: [ICON]
      api.typesv2.common.VisualElement left_icon = 1;
      // ex: 0 Forex charges
      api.typesv2.common.Text title = 2;
      // ex: On spends up to ₹30k/month
      api.typesv2.common.Text subtitle = 3;
    }
  }
  //[ICON] header icon for the view
  api.typesv2.common.VisualElement top_icon = 1;
  //Ex: Save more with Fi-Federal Debit Card
  api.typesv2.common.Text title = 2;
  // Ex: On spends up to ₹30k/month
  api.typesv2.common.Text sub_title = 3;
  // Cta to move to next screen
  // Ex: On spends up to ₹30k/month
  api.typesv2.ui.IconTextComponent action = 4;;
  // Container view with list of benefits
  ContainerView container_view = 5;
}

// FIGMA: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=3527-112543&mode=design&t=gh3sCRk4mMBlAMvK-4
message ActiveStateListView {
  // List of benefit
  message BenefitList {
    // [ICON]
    api.typesv2.common.VisualElement left_icon = 1;
    // Ex: Debit Card order charges [info-icon]
    api.typesv2.ui.IconTextComponent title = 2;

    message RightAmount {
      // Amount saved
      // Ex: ₹299
      api.typesv2.common.Text amount = 1;

      // Amount that was paid if not in the tier
      // --499--
      api.typesv2.common.Text strikethrough_amount = 2;
    }

    oneof right_view {
      RightAmount right_amount = 3;
      // collected options
      // [Icon]
      api.typesv2.common.VisualElement icon = 4;
    }

  }
  // List of benefits
  repeated BenefitList benefit_list = 1;
  // Info banner on bottom
  // [Icon] Explore debit card offers [Icon]
  api.typesv2.ui.IconTextComponent info = 2;
}

// This used to B.E purpose only.
enum BenefitsType {
  BENEFITS_TYPE_UNSPECIFIED = 0;
  BENEFITS_TYPE_DEBIT_CARD = 1;
  BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW = 2;
  BENEFITS_TYPE_MORE_INFO_VIEW = 3;
  BENEFITS_TYPE_MONTHLY_VIEW = 4;
  BENEFITS_TYPE_OTHER = 5;
  BENEFITS_TYPE_MONTHLY_TRANSFER_VIEW = 6;
  BENEFITS_TYPE_REWARD_VIEW = 7;
  BENEFITS_TYPE_WARNING_VIEW = 8;
  BENEFITS_TYPE_HEALTH_INSURANCE_VIEW = 9;
}

message GetTierFlowScreenRequest {
  frontend.header.RequestHeader req = 1;
  string meta_data = 2;
}

message GetTierFlowScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink for the requested screen
  // this must contain the screen options of the corresponding link screen if any
  deeplink.Deeplink deeplink = 2;
}

message GetTierAllPlansV2Request {
  frontend.header.RequestHeader req = 1;
  string meta_data = 2;
}

message GetTierAllPlansV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  // Title of the page
  api.typesv2.common.Text title = 2;
  // tier plan to focus on in all tier plans
  // this will be an index for a tier plan in list of tier plans
  int32 tier_index_to_focus = 3;
  repeated TierPlan tier_plans = 4;
}

message TierPlan {
  // Gradient background color
  api.typesv2.common.ui.widget.BackgroundColour header_background_color = 1;
  // Header overlay background color
  api.typesv2.common.ui.widget.BackgroundColour header_overlay_background_color = 2;
  // Content background color
  api.typesv2.common.ui.widget.BackgroundColour content_background_color = 3;
  // Plan Icon
  api.typesv2.common.VisualElement icon = 4;
  // Icon used for swipe action
  // Ref : https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10002-56105&m=dev
  // We are keeping it single at client we will decide which icon to show based on the tier plan
  api.typesv2.common.VisualElement swipe_icon = 5;
  PlanCard plan_card = 6;
  repeated PlanBenefit plan_benefits = 7;
  // Next action cta
  deeplink.Cta cta = 8;
  // Toolbar right image
  api.typesv2.common.VisualElement toolbar_right_image = 9;
  // Toolbar right image deeplink
  deeplink.Deeplink toolbar_right_image_deeplink = 10;
  // Metadata for plan which is rendered.
  // Deprecated: use plan_meta_data instead
  string plan_meta = 11 [deprecated = true];
  map<string, string> plan_meta_map = 12;
  // client should apply the `nav_bar_bg_color` of the currently displayed plan page to the navigation bar.
  api.typesv2.common.ui.widget.BackgroundColour nav_bar_bg_color = 13;
}

// May contain benefits of the plan or info about the plan or banners
message PlanBenefit {
  // Deprecated: use bg_color from container_property instead
  api.typesv2.common.ui.widget.BackgroundColour background_color = 1 [deprecated = true];
  // Title of the info
  api.typesv2.ui.IconTextComponent title = 2;
  oneof benefit {
    BenefitCard benefit_card = 3;
    EntryBannerSmall small_entry_banner = 4;
    EntryBanner entry_banner = 5;
    InfoBanner info_banner = 6;
    LearnMoreBanner learn_more_banner = 7;
  }
  // On tap of the whole card, if deeplink is there, need to navigate
  deeplink.Deeplink deeplink = 8;
  // Metadata for benefit which is shown on tap of this.
  string benefit_meta = 9;
  // Container property for the benefit component.
  // Client should apply bg_color, padding, margin etc from container_property.
  api.typesv2.ui.sdui.properties.ContainerProperty container_property = 10;
}

message GetAMBScreenDetailsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetAMBScreenDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.ui.sdui.sections.Section section = 2;
  api.typesv2.common.Text title = 3;
}

