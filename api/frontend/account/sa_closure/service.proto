syntax = "proto3";

package frontend.account.sa_closure;

import "api/frontend/account/enums/sa_closure.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/frontend/account/sa_closure";
option java_package = "com.github.epifi.gamma.api.frontend.account.sa_closure";

service SavingsAccountClosure {
  // GetSaClosureFlow rpc is the orchestrator api which decides the page where user should be
  // landed based on status of closure request the user is in
  // lands on benefits screen if user hasn't started the closure process
  rpc GetSaClosureFlow (GetSaClosureFlowRequest) returns (GetSaClosureFlowResponse);
  // GetSaClosureBenefits rpc returns the benefits user would miss on closing fi account
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=316-19762&mode=design&t=YkbGp6iMfUmYLSLx-4
  rpc GetSaClosureBenefits (GetSaClosureBenefitsRequest) returns (GetSaClosureBenefitsResponse);
  // Submits the feedback entered by the user and returns the deeplink for next action
  rpc SubmitSaClosureUserFeedback (SubmitSaClosureUserFeedbackRequest) returns (SubmitSaClosureUserFeedbackResponse);
  // lists the criteria user has to complete before submitting the closure request
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=313-14779&mode=design&t=YkbGp6iMfUmYLSLx-4
  rpc FetchSaClosureCriteriasForUser (FetchSaClosureCriteriasForUserRequest) returns (FetchSaClosureCriteriasForUserResponse);
  // rpc to submit user entered pan and dob details for validation
  // on failure of validation user is taken to an error screen https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=316-25851&mode=design&t=dL7LJ7PujX6uecF3-4
  rpc ValidatePanDobForSaClosure (ValidatePanDobForSaClosureRequest) returns (ValidatePanDobForSaClosureResponse);
  // submits closure request
  // upon submission user will be allowed to cancel request within 10 (config driven) days,
  // after which the closure request will be sent to Ops to proceed with the closure
  rpc SubmitClosureRequest (SubmitClosureRequestRequest) returns (SubmitClosureRequestResponse);
  // when user clicks 'I'll close account later' before submitting the closure request - we mark closure request as CANCELLED_BEFORE_SUBMISSION
  // when user clicks 'cancel my closure request' after submission within the time window - we mark closure request as CANCELLED_MANUALLY
  // a new request is created when user comes back to closure flow
  rpc CancelClosureRequest (CancelClosureRequestRequest) returns (CancelClosureRequestResponse);
  // Evaluates user on all eligible criterias and freeze on account
  rpc EvaluateUserForClosureEligibility (EvaluateUserForClosureEligibilityRequest) returns (EvaluateUserForClosureEligibilityResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_tokenization) = true;
    option (rpc.device_integrity_check) = NOT_REQUIRED;
    option (rpc.skip_device_integrity_check) = true;
  };
}

message GetSaClosureFlowRequest {
  frontend.header.RequestHeader req = 1;
  enums.SAClosureRequestEntryPoint entry_point = 2;
}

message GetSaClosureFlowResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // Internal error while processing the request
    INTERNAL = 13;
    // status code when operational status api fails
    FETCH_OPERATIONAL_STATUS_FAILURE = 101;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink to redirect user on entering account closure flow
  frontend.deeplink.Deeplink next_action = 2;
}

message GetSaClosureBenefitsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetSaClosureBenefitsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.common.Text page_title = 2;
  api.typesv2.ui.IconTextComponent benefits_section_header = 3;
  // list of benefit items with title, subtitle and a visual element
  repeated api.typesv2.common.ui.widget.VisualElementTitleSubtitleElement benefit_items = 4;
  // proceeds to next screen of account closure
  deeplink.Cta proceed_cta = 5;
  // return user to previous screen
  deeplink.Cta cancel_cta = 6;
}

message SubmitSaClosureUserFeedbackRequest {
  frontend.header.RequestHeader req = 1;
  // feedback given by user
  // either user selected feedback option or user's custom feedback text
  string feedback_text = 2;
  // when user clicks resolve my issues, a support ticket is created
  bool create_support_ticket = 3;
}

message SubmitSaClosureUserFeedbackResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message FetchSaClosureCriteriasForUserRequest {
  frontend.header.RequestHeader req = 1;
}

message FetchSaClosureCriteriasForUserResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // Internal error while processing the request
    INTERNAL = 13;
    // status code when fetching of any criteria item fails
    FETCH_FAILED = 101;
  }
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.common.Text page_title = 2;
  api.typesv2.common.Text page_subtitle = 3;
  // list of criterias to be fulfilled in groups
  repeated CriteriaGroup criteria_groups = 4;
  // cta is disabled when any one of the eligible criterias are not fulfilled
  deeplink.Cta proceed_cta = 5;
  // text to be displayed when user tries to click proceed button without fulfilling all criterion
  api.typesv2.common.Text disallow_proceed_text = 6;
}

message CriteriaGroup {
  api.typesv2.ui.IconTextComponent group_number = 1;
  // eg: clear outstanding loans
  api.typesv2.common.Text group_heading = 2;
  // zero state components
  CriteriaGroupZeroState zero_state = 3;
  // list of criteria in a particular group
  // empty for zero state
  repeated CriteriaItem criteria_items = 4 [deprecated = true];
  // optional: any info text that needs to be conveyed to the user
  // eg: If left unused, your Fi-Coins will automatically expire once you close your account.
  api.typesv2.ui.IconTextComponent info_text = 5;
  string bg_color = 6;
  // up arrow icon is sent
  // on click the group is collapsed and icon is inverted
  api.typesv2.common.VisualElement collapse_icon = 7;
  bool is_collapsed = 8;
  // enums.SaClosureCriteriaGroup.String() - used to identify the group
  string group_identifier = 9;

  oneof criteria_items_v2 {
    CriteriaItemList criteria_item_list = 10;
    // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=6594-44095&t=Ck5egBSrYgsB7XLy-4
    FullGroupCriteriaItem full_group_criteria_item = 11;
  }
}

message CriteriaItemList {
  repeated CriteriaItem criteria_items = 1;
}

message CriteriaGroupZeroState {
  // icon to display when no action has to be taken on the criteria group
  api.typesv2.common.VisualElement zero_state_icon = 4;
  // text to display when no action has to be taken on the criteria group
  api.typesv2.common.Text zero_state_text = 5;
}

message CriteriaItem {
  // eg: Fi coin icon
  api.typesv2.common.VisualElement icon = 1;
  // eg: Smart Deposit (total)
  api.typesv2.ui.IconTextComponent criteria_name = 2;
  // eg: ₹8000 - amount remaining in smart deposit
  // can display failure response as well if fetching of criteria data fails
  api.typesv2.ui.IconTextComponent criteria_value = 3;
  // Cta to fulfill the criteria
  // takes the user to a screen to fulfill a criteria
  api.typesv2.ui.IconTextComponent complete_cta = 4;
  string bg_color = 5;
  // information needed by BE for validations
  CriteriaItemMeta meta = 6;
  // enums.SaClosureCriteriaItem.String() - used to identify the item
  string item_identifier = 7;
}

message FullGroupCriteriaItem {
  // information needed by BE for validations
  CriteriaItemMeta meta = 1;
  CriteriaGroupZeroState display_info = 2;
  api.typesv2.ui.IconTextComponent retry_cta = 6;
  // enums.SaClosureCriteriaItem.String() - used to identify the item
  string item_identifier = 7;
}

message CriteriaItemMeta {
  bool fetch_failed = 1;
  // if criteria is soft, next action is enabled without satisfying the criteria
  bool soft_criteria = 2;
  oneof value {
    google.type.Money money = 3;
    int32 fi_coins = 4;
    // for crierias which doesn't have a value, but will be blocking the closure
    // eg. fi account linked as UPI lite in other platforms
    bool is_blocking = 5;
  }
}

message ValidatePanDobForSaClosureRequest {
  frontend.header.RequestHeader req = 1;
  // user entered pan
  string pan = 2;
  // user entered DOB
  google.protobuf.Timestamp dob = 3;
}

message ValidatePanDobForSaClosureResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // next action for user after successful validation
  deeplink.Deeplink next_action = 2;
}

message SubmitClosureRequestRequest {
  frontend.header.RequestHeader req = 1;
}

message SubmitClosureRequestResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // Internal error while processing the request
    INTERNAL = 13;
    // status code when operational status api fails
    FETCH_OPERATIONAL_STATUS_FAILURE = 101;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // next action for user after submission of closure request
  deeplink.Deeplink next_action = 2;
}

message CancelClosureRequestRequest {
  frontend.header.RequestHeader req = 1;
}

message CancelClosureRequestResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message EvaluateUserForClosureEligibilityRequest {
  frontend.header.RequestHeader req = 1;
  string actor_id = 2;
}

// Represents the status of a specific SA closure criteria item
message SaClosureCriteriaItemStatus {
  // The criteria item enum
  enums.SaClosureCriteriaItem criteria_item = 1;
  // Whether this criteria item has failed (true if failed, false if completed/satisfied)
  bool is_failed = 2;
}

message EvaluateUserForClosureEligibilityResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // Internal error while processing the request
    INTERNAL = 13;
    // status code when operational status api fails
    FETCH_OPERATIONAL_STATUS_FAILURE = 101;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // true if account has any freeze
  bool has_freeze = 2;
  // true if user did not satisfy any criteria item
  // eg. has not closed jump investments
  bool failed_criteria_check = 3;
  bool has_lien = 4;
  // list of criteria items that user has not fulfilled for account closure
  // Deprecated: Use failed_criteria_items, has_lien, has_freeze, pending_charges_amount, and is_account_closed fields instead.
  // Callers should process the structured data from these fields and create display strings on their end.
  repeated string failure_reasons = 5 [deprecated = true];
  // true if user has any pending charges on the account
  // Deprecated: Use pending_charges_amount instead. Check if amount > 0 to determine if charges exist.
  bool has_pending_charges = 6 [deprecated = true];

  // List of SA closure criteria items with their failure status (true if failed)
  // does not contain soft marked criteria items
  repeated SaClosureCriteriaItemStatus evaluated_criteria_items = 7;

  // Indicates whether the account is already closed
  bool is_account_closed = 8;

  // Pending charges amount (includes GST, rounded up).
  // This field is populated  if the user has pending charges.
  // Clients should check for both a nil value and a zero amount
  // to determine whether the user has any pending charges.
  google.type.Money pending_charges_amount = 9;
}
