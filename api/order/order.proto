// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package order;

import "api/frontend/deeplink/deeplink.proto";
import "api/order/payment/transaction.proto";
import "api/order/workflow.proto";
import "api/typesv2/common/ownership.proto";
import "api/queue/consumer_headers.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/order";
option java_package = "com.github.epifi.gamma.api.order";


// We define an order as a workflow for exchange of goods and services between two actors in our system with minimum
// one transaction involved.
message Order {
  string id = 1;

  // actor who initiated the order
  string from_actor_id = 2;

  // receiving entity of the order or the service provider
  string to_actor_id = 3;

  // workflow followed by the order
  order.OrderWorkflow workflow = 4;

  // order's current status
  OrderStatus status = 5;

  // An opaque blob containing the data needed for fulfillment of an order.
  // This might vary based on the type of order. The data inside the blob will
  // depend on underlying domain service.
  bytes order_payload = 6;

  OrderProvenance provenance = 7;

  // Final order amount. (Incl. of all taxes and service charges)
  google.type.Money amount = 8;

  // order creation timestamp
  google.protobuf.Timestamp created_at = 9;

  // order last updated time stamp
  google.protobuf.Timestamp updated_at = 10;

  // order deleted time stamp
  google.protobuf.Timestamp deleted_at = 11;

  // Defines the time till which an order is valid. A payment/transactions must not be initialized beyond
  // if the order has expired because of following reason.
  // 	1) Some orders can possibly have expiration associated with them .. taking a typical case of gold
  //	   transfer where price of an item say gold fluctuates and is locked for the next 5 minutes.
  //	2)  UPI collect requests which comes with a validity time.
  //  3) Orders associated with P2P Fund transfer can have risk factor associated with them (in terms of unlocked phone exposure)
  //     in case if we allow transaction for order created lets say 1 hour ago.
  google.protobuf.Timestamp expire_at = 12;

  // external is internal id equivalent for an order,
  // that can be shared with actor or any other external system inorder to identify an order uniquely in the system.
  // It can be typically used in places where for
  // security reasons we don't want to expose internal id to the outside world
  string external_id = 13;

  // Defines the tag associated with the order, e.g. Deposit, FD, etc.
  // An order can be associated with multiple tags.
  repeated OrderTag tags = 14;

  // Optional: Signifies the UI entrypoint from which order was created.
  // Only to be populated for app initiated transactions
  UIEntryPoint ui_entry_point = 15;

  // request id sent by client in case idempotency around
  // order creation is important for the caller use case. This can be typically
  // used for automated workflows like B2C_FUND_TRANSFER where single order per request
  // is important.
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_req_id = 16;


  // unique identifier to map an order to a celestial workflow request
  // a single workflow request can have more than one order in general
  string workflow_ref_id = 17;

  // Obfuscated GPS coordinates location identifier for the payer.
  // Since, location identifier is a sensitive user information, it's not
  // recommended to store this data directly in the order domain object.
  // A location token is a place holder for the exact user co-ordinates.
  // Co-ordinates for the specified token can be fetched using location service's
  // GetCoordinates RPC
  // Note - Optional field, will be populated only if the payer's location is known
  string from_actor_location_token = 18;

  // Obfuscated GPS coordinates location identifier for the payee.
  // Since, location identifier is a sensitive user information, it's not
  // recommended to store this data directly in the order domain object.
  // A location token is a place holder for the exact user co-ordinates.
  // Co-ordinates for the specified token can be fetched using location service's
  // GetCoordinates RPC
  // Note - Optional field, will be populated only if the payee's location is known
  string to_actor_location_token = 19;

  // This deeplink is where user is redirected post authorisation of payment
  // If not specified client is redirected to fund transfer status screen by default.
  // We are not directly using deeplink here to avoid frontend dependency in the root domain proto
  // [deprecated in favour of post_auth_deeplink_v1 in order to have some validation on type of
  // object passed by client and also to avoid multiple conversions of deeplink to Any object and
  // vice-versa]
  google.protobuf.Any post_auth_deeplink = 20 [deprecated = true];

  // This deeplink is where user is redirected post payment. Irrespective of it's state
  // i.e. success or failure.
  // If not passed by default, it takes user to home (in case of add-funds),
  // and to receipt or post-payment-success screen etc.
  // [deprecated in favour of post_payment_deeplink_v1 in order to have some validation on type of
  // object passed by client and also to avoid multiple conversions of deeplink to Any object and
  // vice-versa]
  google.protobuf.Any post_payment_deeplink = 21 [deprecated = true];

  // This deeplink is where user is redirected post authorisation of payment
  // If not specified client is redirected to fund transfer status screen by default.
  // NOTE - post_auth_deeplink_v1 will be given priority over post_auth_deeplink
  frontend.deeplink.Deeplink post_auth_deeplink_v1 = 22;

  // This deeplink is where user is redirected post payment. Irrespective of it's state
  // i.e. success or failure.
  // If not passed by default, it takes user to home (in case of add-funds),
  // and to receipt or post-payment-success screen etc.
  // NOTE - post_payment_deeplink_v1 will be given priority over post_payment_deeplink
  frontend.deeplink.Deeplink post_payment_deeplink_v1 = 23;
}

// Different status through which order state machine transitions
// through an order lifetime.
// Note- the transition workflow of state machine will change per order type.
// e.g. one change can be some order need payment before fulfillment stage while
// some need it after fulfillment, the state transition will vary for both of these
// use cases.
enum OrderStatus {
  ORDER_STATUS_UNSPECIFIED = 0;

  // Entry created in the system.
  // Ideally, order creation is followed by transaction creation in that case order is moved to IN_PAYMENT.
  // Thus, if order in CREATED state can also be inferred as failure of transaction creation or an abandoned order.
  CREATED = 1;

  // Attempting payment for the order.
  // An order is moved to IN_PAYMENT once transaction initialization is successful.
  IN_PAYMENT = 2;

  // Payment is successful.
  // An order is usually updated as PAID once payment system publish payment successful event to the order orchestrator.
  PAID = 3;

  // Payment attempt failed for the order.
  // An order is usually updated as FAILED once payment system publishes payment failure event to the order orchestrator.
  PAYMENT_FAILED = 4;

  // Fulfillment in progress.
  // This can have multiple meaning based on workflow.
  // Good / Service being activated for the actor.
  // e.g. for RECHARGE workflow this step can signify recharge is in progress.
  IN_FULFILLMENT = 5 [deprecated = true];

  // Fulfillment successful.
  // This can have multiple meaning based on workflow.
  // Goods / Services received by the actor who created the order
  // e.g. for RECHARGE workflow this step can signify recharge is successful.
  FULFILLED = 6 [deprecated = true];

  // Fulfillment failed.
  // Goods / Services couldn't not received by the actor who made the payment.
  FULFILLMENT_FAILED = 7 [deprecated = true];

  // Payment to the service provider is being processed
  IN_SETTLEMENT = 8 [deprecated = true];

  // Payment successful to the service provider
  SETTLED = 9 [deprecated = true];

  // Payment to the service provider failed
  SETTLEMENT_FAILED = 10 [deprecated = true];

  // Order needs human intervention due to unexpected failures.
  // It can occur due to various scenarios but not limited to:
  // 1) retries exhaustion while one of transaction is still in non-terminal status.
  // 2) one of transaction failed while other succeeded
  MANUAL_INTERVENTION = 11;

  // Collect request registration with vendor is in progress.
  // Typically, an order is in this state when there is an RPC failure due to which order enters an unknown state.
  // Order state machine is finally moved to a terminal state based on status call for vendor
  // An order is moved to COLLECT_REGISTERED once collect registration is successful
  COLLECT_IN_PROGRESS = 12;

  // Collect request has been declined by the payer
  COLLECT_DISMISSED_BY_PAYER = 13;

  // Collect request has been cancelled by the payee or the initiator
  COLLECT_DISMISSED_BY_PAYEE = 14;

  // Collect request was successfully registered with the vendor
  COLLECT_REGISTERED = 15;

  // Collect requested registration has failed. This can be typically due to a permanent
  // failure at vendor's end.
  // this can be due to various reasons some of them are invalid payer address, invalid payee address,
  // missing fields in the request or validation error fo some sorts
  COLLECT_FAILED = 16;

  // Order has expired.
  // Different order workflows have different expiry duration.
  // This happens when payment authorisation doesn't come from
  // the payer with in the stipulated time window
  EXPIRED = 17;

  // Order is rejected by the system when the client initiates an order creation request.
  // An order is rejected usually when one of the velocity or validation checks fail on the server side.
  REJECTED = 18;

  // The payment for order is reversed.
  // For eg. in case of fund transfer the order can be reversed after a successful debit
  // in case the credit fails
  // An order can move into PAYMENT_REVERSED state even from PAYMENT_FAILED/PAID state.
  PAYMENT_REVERSED = 19;


  // Order payment authorisation has finished and corresponding transactions(s) are ready to be initiated
  // with partner bank
  INITIATED = 20;
}

// Important: Provenance used may affect transaction handling in scenarios such as
// reversal. E.g. In case the provenance is USER_APP, INTERNAL the orders initiated would be
// eligible for In-App reversal. Please be mindful while passing this value in order creation.
// Provenance: the beginning of something's existence; something's origin.
// An order can be created in the system from different entry points.
// e.g. APP, ATM, POS, etc.
// This enum represents different entry provenance of order in the system
enum OrderProvenance {
  ORDER_PROVENANCE_UNSPECIFIED = 0;

  // Signifies the order was created from the user APP.
  USER_APP = 1;

  // Signifies order was conceived in an external system
  // Few scenarios when order provenance is marked external-
  // 1) there was an offline transaction (credit/debit) in user's account and epiFi got notified either
  // using notification from vendor or by statement sync mechanism
  // 2) When an external PSP initiates a payment/collect request against epiFi user's VPA in UPI.
  EXTERNAL = 2;

  // Signifies order was conceived from an internal process in the system.
  // Few scenarios when order provenance is marked internal -
  // 1) Deposit creation with add funds flow. In this case, an order with URN_TRANSFER workflow is created from the
  // user's app, which internally on successfully processing creates another order with workflow CREATE_DEPOSIT.
  INTERNAL = 3;

  // Signifies that the order was created due to an ATM card transaction
  // Few scenarios when order provenance is marked ATM -
  // 1) Epifi received a notification for atm withdrawal
  ATM = 4;

  // Signifies that the order was created due to POS transaction
  // Few scenarios when order provenance is marked POS -
  // 1) Epifi received a notification for POS transaction
  POS = 5;

  // Signifies that the order was created due to ECOMM transaction
  // Few scenarios when order provenance is marked ECOMM -
  // 1) Epifi received a notification for ECOMM transaction
  ECOMM = 6;

  // Signifies that order was created due to third party application eg. Intent, QR
  THIRD_PARTY = 7;

  // Signifies order was created as a result of partner mobile banking payment
  // Few scenarios when order provenance is marked PARTNER_MOBILE_BANKING -
  // 1) User goes to partner mobile banking app and does a IMPS/NEFT/RTGS payment.
  PARTNER_MOBILE_BANKING = 8;

  // Signifies order was created due to a appeasement payout from customer support team
  SHERLOCK = 9;

  // Signifies order was created from Bank side.
  // Few cases would be like: ATM charges, ECS charges etc.
  VENDOR_BANK = 10;

  // Signifies order was created from to VISA Money Transfer(VMT) system.
  // Usually E-Commerce web uses VMT for refunding card transactions.
  VISA_MONEY_TRANSFER = 11;

  // Signifies order was created as a result of partner net banking payment
  // Few scenarios when order provenance is marked PARTNER_NET_BANKING -
  // 1) User goes to website  and does a IMPS/NEFT/RTGS payment.
  PARTNER_NET_BANKING = 12;
}


// OrderFieldMask is the enum representation of all the order fields.
// Meant to be used as field mask to help with database updates
enum OrderFieldMask {
  ORDER_FIELD_MASK_UNSPECIFIED = 0;
  ID = 1;
  FROM_ACTOR_ID = 2;
  TO_ACTOR_ID = 3;
  WORKFLOW = 4;
  STATUS = 5;
  ORDER_PAYLOAD = 6;
  PROVENANCE = 7;
  AMOUNT = 8;
  CREATED_AT = 9;
  UPDATED_AT = 10;
  EXPIRE_AT = 11;
  ALL = 12;
  TAGS = 13;
  EXTERNAL_ID = 14;
  WORFLOW_REF_ID = 15;
  FROM_ACTOR_LOCATION_TOKEN = 16;
  TO_ACTOR_LOCATION_TOKEN = 17;
  // NOTE: client_req_id is a critical piece for performing any sort of updates and should not be updated blindly without any validations.
  CLIENT_REQ_ID = 18;
}

// OrderWithTransactions is a wrapper message containing order along with a list of transactions associated with it.
message OrderWithTransactions {
  // Order object containing order data.
  Order order = 1;

  // list of transactions associated with an order.
  // an order can have more than one transactions under-neath
  repeated order.payment.Transaction transactions = 2;
}

// orderAccountRelation tells regarding the relation between the order and its respective account
enum OrderAccountRelation {
  ORDER_ACCOUNT_RELATION_UNSPECIFIED = 0;
  // internal - if order belongs to internal account(fi savings account)
  INTERNAL_ACCOUNT = 1;
  // external - if order doesn't belong to internal accounts(like: Tpap account)
  EXTERNAL_ACCOUNT = 2;
}

// OrderUpdate is the message to be published to a topic when there is a update in order state
message OrderUpdate {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;

  OrderWithTransactions orderWithTransactions = 2;

  // orderAccountRelation will tell if the order is for internal account or not
  OrderAccountRelation orderAccountRelation = 3;

  // ownership denotes the RE level entity-segregation ownership for which this update is being published.
  // This ownership does not convey the row level entity segregation of the txns (i.e. it returns ownership
  // as EPIFI_TECH, for both FEDERAL_BANK & EPIFI_TECH owned transactions). In other words, it denotes the ownership
  // which should be used to find the DB to connect to. In order to determine the actual ownership of the transaction,
  // check the ownership field contained in the transaction payload.
  api.typesv2.common.Ownership ownership = 4;
}

// BatchOrderUpdate is the message to be published to a topic after batch of order is updated
message BatchOrderUpdate {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;

  repeated Order orders = 2;
}

// A stage is accumulation of similar order states.
// E.g. PAYMENT is order stage under which order state machine goes through IN_PAYMENT, PAID and PAYMENT_FAILED states.
// Similarly, other order stages are FULFILLMENT, SETTLEMENT, REFUND, etc.
enum OrderStage {
  ORDER_STAGE_UNSPECIFIED = 0;

  // typically, an order in CREATED state is in CREATION state
  CREATION = 1;

  // Under payment stage order goes through IN_PAYMENT, PAID, PAYMENT_FAILED, COLLECT_REGISTERED, COLLECT_FAILED,
  // COLLECT_IN_PROGRESS,  COLLECT_DISMISSED_BY_PAYER and COLLECT_DISMISSED_BY_PAYEE states
  // This stage involves money movement from user's account.
  PAYMENT = 2;

  // Under fulfillment stage order goes through IN_FULFILLMENT, FULFILLED, FULFILLMENT_FAILED states
  // This stage involves provisioning of services to the user
  FULFILLMENT = 3;

  // Under fulfillment stage order goes through IN_SETTLEMENT, SETTLED, SETTLEMENT_FAILED states
  // This stage involves money movement to the service provider account
  // It can be automated in some cases and in some case triggered by the user depending on the workflow
  SETTLEMENT = 4;

  // NO_OP(No operation) means non-operation stage
  // It is used in workflow map to signify end of order workflow
  ORDER_STAGE_NO_OP = 5;

  // Under COLLECT stage order goes through `COLLECT_IN_PROGRESS`, `COLLECT_REGISTERED`, `COLLECT_FAILED` states
  // The stage involves initializing the collect request from the user's account
  COLLECT = 6;
}

// OrderTags includes all the tags an order can have
enum OrderTag {
  ORDER_TAGS_UNSPECIFIED = 0;
  DEPOSIT = 1;
  FD = 2;
  SD = 3;
  RD = 4;
  MERCHANT = 5; // to differentiate between merchant and P2P payments
  REWARD = 6; // to identify orders associated with rewards like SD Reward, Cash Reward etc.
  FIT = 7; // to identify orders associated with fit
  INTEREST = 8; // to identify orders associated with interest payout
  CASH = 9; // to identify orders associated with cash deposits/withdrawals
  INTERNATIONAL = 10; //to identify international orders
  WALLET = 11; // to identify transactions like wallet top ups
  MUTUAL_FUND = 12; // to identify transactions related to mutual funds
  BHARAT_QR = 13; // to identify bharat qr transactions
  STANDING_INSTRUCTION = 14; // to identify standing instruction orders
  UPI_MANDATES = 15; // to identify upi mandates orders
  LOAN = 16;
  EMI = 17;
  US_STOCKS = 18; // To identify US Stocks orders
  JUMP_P2P_INVESTMENT = 19; // To identify jump/P2P investment orders. Note: we are not populating this for all orders
  // deprecated in favour of US_STOCKS_DIVIDEND_GST_DEBIT and US_STOCKS_SELL_GST_DEBIT
  US_STOCKS_GST = 20 [deprecated = true]; // To identify GST debit for US Stocks
  FUND_TRANSFER_V1 = 21; // To identify fund transfer celestial orders. These workflows support direct notification initiation from the workflow
  US_STOCKS_DIVIDEND_GST_DEBIT = 22; // to identify GST debit on receiving dividends for US Stocks
  US_STOCKS_SELL_GST_DEBIT = 23; // to identify GST debit on receiving funds back for sell of US Stocks
  US_STOCKS_DIVIDEND_CREDIT = 24; // to identify dividend credit for us-stocks
  US_STOCKS_SELL_CREDIT = 25; // to identify sell credit for US stocks
  // deprecated as we are further bifurcating ECS_ENACH_CHARGES on more granular level.
  // use NACH_RETURN_CHARGE, ECS_RETURN_CHARGE or ECS_ENACH_MANDATE_CHARGE for correct identifcation of charges
  ECS_ENACH_CHARGES = 26;
  US_STOCKS_INWARD_REMITTANCE_GST_REFUND = 27; // to identify refund credit for GST charges on US-Stocks Inwards remittance
  UPI_LITE_ACTIVATION = 28; // to identify order created while UPI Lite activation
  UPI_LITE_DEACTIVATION = 29; // to identify order created while UPI Lite deactivation
  UPI_LITE_TOP_UP = 30; // to identify order created while UPI Lite top up
  CHEQUE_BOOK_CHARGES = 31; // To identify cheque book order debit
  LOAN_EARLY_SALARY = 32; // To identify early salary si order
  US_STOCKS_AGGREGATED_INWARD_REMITTANCE = 33; // aggregated remittance txn for US stocks sell-orders and dividends
  US_STOCKS_AGGREGATED_INWARD_REMITTANCE_GST_DEBIT = 34; // GST debit on aggregated US stocks remittance txn
  DC_FOREX_MARKUP_REFUND = 35; // To identify debit card forex markup refund
  ENACH = 36; // to identify enach orders.
  // to identify charges in case of NACH return. NACH returns happens when we have insufficient balance for any e-NACH created on our account.
  NACH_RETURN_CHARGE = 37;
  ECS_RETURN_CHARGE = 38;  // to identify charges in case of ECS return. ECS Return is same as NACH return just the protocal is different
  ANYWHERE_BANKING_CHARGE = 39; // to identify charges in case of cash deposit/withdrawal at physical branches
  ATM_DECLINE_CHARGE = 40; // to identify charges for insufficient balance during ATM withdrawal
  DUPLICATE_CARD_FEE = 41; // to identify fee incurred during debit card replacement
  OTHER_BANK_ATM_CHARGE = 42; // to identify charge for cash deposit/withdrawal from any ATM other than account associated Vendor bank ATM
  CASH_HAND_CHARGE = 43; // to identify charges for handling cash at physical branches. It has some overlapping with ANYWHERE_BANKING_CHARGE
  ECS_ENACH_MANDATE_SETUP_CHARGE = 44; // to identify charges debited by bank setup of ecs/nach mandate.
  DEBIT_CARD_CHARGES = 45; // to identify orders related to debit card charges eg: Physical debit card dispatch.
  CREDIT_CARD_PAYMENT = 46; // to identify orders related to credit card payment
  ECOM_POS_DECLINE_CHARGE = 48; // to identify charges for insufficient balance during ECOM/POS transactions
  DEBIT_CARD_AMC_CHARGE = 49; // to identify AMC charges for debit card.
  RAZORPAY = 50; // to identify order related to razorpay
  POST_DEBIT_CHARGE = 51; // Charges debited by a vendor post the original transaction
  DC_FOREX_MARKUP_CHARGE = 52; // to identify debit card forex markup charge
  DC_TCS_FEE_CHARGE = 53; // to identify debit card TCS fee charge
  DC_DCC_FEE_CHARGE = 54; // to identify debit card DCC fee charge
  CHEQUE = 55; // to identify cheque related orders
  CHEQUE_REVERSAL = 56; // to identify cheque reversal/bounces related orders
  PG_REFUND = 57; // to identify orders that are created due to refunds to customers initiated from payment gateway
  DC_MANDATE_REGISTER = 58; // to identify debit card mandate registration transaction
  DC_MANDATE_PAYMENT = 59; // to identify debit card mandate payment transaction
  AMB_CHARGE = 60; // to identify orders corresponding to non-maintenance of AMB (Average Monthly Balance) charges
  PREDICTED_MERCHANT = 61; // to identify whether a NEFT/IMPS/RTGS credit transaction to a user has been made by a merchant based on the remitter name.
  NFC = 62; // to identify orders made via NFC payment mode
  FIRST_CARD_ORDER_FEE = 63; // to identify orders corresponding to first card order fee
  INTL_ATM_CHARGE = 64; // to identify charges posted in a user's savings account for transacting in an ATM outside india
  TOD_CHARGE = 65; // to identify temporary over draft charges
  RECHARGE_PAYMENT = 66; // to identify recharge mobile transactions
}

enum UIEntryPoint {
  // unspecified
  UI_ENTRY_POINT_UNSPECIFIED = 0;
  // Signifies order was created using timeline
  // wil be used as default in case entry point is unspecified
  TIMELINE = 1;
  // Signifies order was created for secure QR code scan
  SECURE_QR_CODE = 2;
  // Signifies order was created using insecure QR code scan
  INSECURE_QR_CODE = 3;
  // Signifies order was created using secure intent payment
  SECURE_INTENT = 4;
  // Signifies order was created using insecure intent payment
  INSECURE_INTENT = 5;
  // Signifies order was created using add funds flow during onboarding
  ONBOARD_ADD_FUNDS = 6;
  // Signifies order was created using transfer in flow
  TRANSFER_IN = 7;
  // Signifies add funds order was created from home screen
  HOME = 8;
  // Signifies order was created from account details screen
  ACCOUNT_DETAILS = 9;
  // Signifies order was created from account summary screen
  ACCOUNT_SUMMARY = 10;
  // Signifies order was created from referrals screen
  REFERRALS = 11;
  // Signifies order created from Home screen's persistent Add funds button
  HOME_PERSISTENT_CTA = 12;
  // Signifies order was created from Deposit creation flow, if funds were insufficient
  // for deposit creation
  DEPOSIT_CREATION = 13;
  // Signifies order was created from Bonus jar creation flow, if funds were insufficient
  // for bonus jar creation
  BONUS_JAR_CREATION = 14;
  // Signifies order was created from FITTT screen
  FITTT = 15;
  // Request has been received from Auto Pay hub screen
  AUTO_PAY_HUB = 16;
  // Request has come from One time mutual fund investment screen
  MF_BUY_ONE_TIME = 17;
  // Request has come from physical debit card charges flow
  PHYSICAL_DEBIT_CARD_CHARGES = 18;
  // Signifies order was created from AskFi result summary
  ASK_FI = 19;
  // Signifies order is getting created while activation of UPI Lite
  ACTIVATE_UPI_LITE = 20;
  // Signifies order is getting created for UPI Lite top up
  TOP_UP_UPI_LITE = 21;
  // Signifies order is getting created while deletion of UPI Lite
  DELETE_UPI_LITE = 22;
  // Signifies order is created by pre approved loan
  PRE_APPROVED_LOAN = 23;
  // This entry point is to be used for penny drop flows
  PENNY_DROP = 24;
  // Signifies order is created from secure cc flow
  SECURED_CC = 25;
  // Signifies order was created for secure QR code scan
  // i.e. This entryPoint will be chosen when user tries to pay
  // via qr share and pay option to a verified urn.
  SECURE_QR_SHARE_AND_PAY = 26;
  // Signifies order was created for insecure QR code scan
  // i.e. This entryPoint will be chosen when user tries to pay
  // via qr share and pay option to an unverified urn.
  INSECURE_QR_SHARE_AND_PAY = 27;
  // Signifies order is getting created in AA salary add funds flow
  // figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=19696-28555&mode=design&t=13Y3cc6K5WNDYx1P-0
  AA_SALARY = 28;
  // if a user has insufficient fund in savings account, we show the payment options to add funds
  // figma: https://www.figma.com/design/kLX73V4pRucy8JM71ajH50/%F0%9F%9B%A0-US-stocks%2Fworkfile?node-id=7099-7475&t=csC27maxcurUmfmU-4
  ADD_FUNDS_USS = 29;
  // signifies that the order creation has been done from the preapproved
  // loans prepay's end
  UI_ENTRY_POINT_LOAN_PREPAYMENT = 30;
  // signifies that the order creation has been done to register a mandate
  // using an external route (eg. PG)
  UI_ENTRY_POINT_EXTERNAL_MANDATE_REGISTRATION = 31;
  // Add funds flow initiated from account closure screen for pending charges
  ACCOUNT_CLOSURE_PENDING_CHARGES = 32;
  // Signifies order creation was initiated by pre-funding add funds flow during onboarding
  ONBOARD_ADD_FUNDS_PRE_FUNDING = 33;
  // order creation was initiated from tiering all plans screen
  TIERING_ALL_PLANS_JOIN_PLUS = 34;
  TIERING_ALL_PLANS_JOIN_INFINITE = 35;
  TIERING_ALL_PLANS_JOIN_PRIME = 36;
  // Add funds flow initiated from AMB details screen
  AMB_DETAILS = 37;
  // Signifies order was created from recharge payment flow
  UI_ENTRY_POINT_RECHARGE_PAYMENT = 38;
}

message UpdateInPaymentOrderTimelineRequest {
  // A set of all the common attributes to be contained in a queue consumer request
  queue.ConsumerRequestHeader request_header = 1;

  // order with txn on which the event is published
  OrderWithTransactions orderWithTransactions = 2;
}

// orders and transactions are filtered based upon the combination of status and workflow.
// A workflow can have multiple status which may change with time and for every status we do not want to show to the user
// for eg. OrderWorkflow_REWARDS_ADD_FUNDS_SD is only shown when the status is FULFILLMENT
message OrderStatusAndWorkflowTypeFilter {
  order.OrderStatus order_status = 1 [(validate.rules).enum = {not_in: [0]}];

  repeated order.OrderWorkflow workflows = 2 [(validate.rules).repeated = {min_items: 1}];
}

// TransactionDetailedStatusUpdate is the message to be published to a topic when there is a update in txn detailed status
message TransactionDetailedStatusUpdate {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;
  // transaction that was updated
  order.payment.Transaction transaction = 2;
}
