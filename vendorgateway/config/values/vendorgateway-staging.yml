Application:
  Environment: "staging"
  Name: "vendorgateway"
  IsSecureRedis: true
  SyncWrapperTimeout: 60
  VGAuthSvcSyncWrapperTimeout: 15
  IsStatementAPIEnabled: true
  IsListKeysSimulated: true
  #dmp dispute
  CreateDisputeURL: "https://simulator.staging.pointz.in:8080/test/DMP/v1.0.0/createDispute"
  DisputeStatusCheckUrl: "https://simulator.staging.pointz.in:8080/DMP/v1.0.0/disputeStatusCheck"
  SendCorrespondenceUrl: "https://simulator.staging.pointz.in:8080/DMP/v1.0.0/disputeCorrespondence"
  UploadDocumentUrl: "https://simulator.staging.pointz.in:8080/DMP/v1.0.0/disputeDocument"
  ChannelQuestionnaireUrl: "https://simulator.staging.pointz.in:8080/DMP/v1.0.0/channelQuestionnaire"
  AccountTransactionsUrl: "https://simulator.staging.pointz.in:8080/DMP/v1.0.0/transactions"
  CreateCustomerURL: "https://simulator.staging.pointz.in:9091/createCustomerFederal"
  CreateLoanCustomerURL: "https://simulator.staging.pointz.in:8080/createLoanCustomerFederal"
  LoanCustomerCreationStatusURL: "https://simulator.staging.pointz.in:8080/loanCustomerCreationStatusFederal"
  CheckCustomerStatusURL: "https://simulator.staging.pointz.in:8080/checkCustomerStatusFederal"
  CreateAccountURL: "https://simulator.staging.pointz.in:9091/createAccountFederal"
  CheckAccountStatusURL: "https://simulator.staging.pointz.in:8080/checkAccountStatusFederal"
  DedupeCheckURL: "https://simulator.staging.pointz.in:8080/dedupeCheck"
  EnquireVKYCStatusUrl: "https://simulator.staging.pointz.in:9091/openbanking/enquirevkyc"
  FetchCustomerDetailsUrl: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/GetCustomerDetails"
  EnquireBalanceURL: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/getBalance"
  CkycSearchURL: "https://simulator.staging.pointz.in:9091/ckyc/search"
  GetKycDataURL: "https://simulator.staging.pointz.in:9091/ckyc/download"
  CreateVirtualIdURL: "https://simulator.staging.pointz.in:8080/createVirtualIdFederal"
  GetTokenURL: "https://simulator.staging.pointz.in:8080/listKeys"
  DeviceRegistrationURL: "https://simulator.staging.pointz.in:9091/registerDevice"
  SetPINURL: "https://simulator.staging.pointz.in:8080/SetCredFederal"
  UPIBalanceEnquiryURL: "https://simulator.staging.pointz.in:8080/BalEnq"
  ReqComplaintURL: "https://simulator.staging.pointz.in:8080/reqComplaint"
  ReqCheckComplaintStatusUrl: "https://simulator.staging.pointz.in:8080/checkComplaintStatus"
  ValidateAddressURL: "https://simulator.staging.pointz.in:8080/ValAddFederal"
  GenerateUpiOtpURL: "https://simulator.staging.pointz.in:8080/generateUpiOtp"
  RespAuthDetailsURL: "https://simulator.staging.pointz.in:8080/RespAuthDetails"
  ReqPayURL: "https://simulator.staging.pointz.in:8080/ReqPay"
  RegisterMobileURL: "https://simulator.staging.pointz.in:8080/registerMobile"
  ListUpiKeyUrl: "https://simulator.staging.pointz.in:8080/ListKeys"
  ListAccountURL: "https://simulator.staging.pointz.in:8080/ListAccount"
  ListAccountProviderURL: "https://simulator.staging.pointz.in:8080/ListAcctProvider"
  RespTxnConfirmationURL: "https://simulator.staging.pointz.in:8080/RespTxnConfirmationFederal"
  RespValidateAddressURL: "https://simulator.staging.pointz.in:8080/RespValAddFederal"
  ReqCheckTxnStatusURL: "https://simulator.staging.pointz.in:8080/ReqCheckTxnStatusFederal"
  ListVaeURL: "https://simulator.staging.pointz.in:8080/ListVaeFederal"
  ReqMandateURL: "https://simulator.staging.pointz.in:8080/ReqMandate"
  RespAuthMandateURL: "https://simulator.staging.pointz.in:8080/RespAuthMandate"
  RespMandateConfirmationURL: "https://simulator.staging.pointz.in:8080/RespMandateConfirmation"
  RespAuthValCustURL: "https://simulator.staging.pointz.in:8080/RespAuthValCust"
  ReqActivationUrl: "https://simulator.staging.pointz.in:8080/ActivateInternationalPayments"
  ListPspURL: "https://simulator.staging.pointz.in:8080/ListPsp"
  GetMapperInfoURL: "https://simulator.staging.pointz.in:8080/GetMapperInfo"
  RegMapperURL: "https://simulator.staging.pointz.in:8080/RegMapper"
  ReqValQRUrl: "https://simulator.staging.pointz.in:8080/ValidateInternationalQR"
  GetUpiLiteURL: "https://simulator.staging.pointz.in:8080/GetUpiLite"
  SyncUpiLiteInfoURL: "https://simulator.staging.pointz.in:8080/SyncUpiLiteInfo"

  AclSftp:
    User: "deploy-comms-kaleyra"
    Host: "sftp.deploy.pointz.in"
    Port: 22

  PanProfileURL: "https://simulator.staging.pointz.in:9091/karza/panProfile"

  BureauIdUrl: "https://api.bureau.id/transactions"

  # send vkyc data to federal for inhouse vkyc service
  SendAgentDataURL: "https://simulator.staging.pointz.in:9091/vkyc/federal/send-agent-data"
  SendAuditorDataURL: "https://simulator.staging.pointz.in:9091/vkyc/federal/send-auditor-data"

  # EPAN Url
  GetEPANKarzaStatusURL: "https://simulator.staging.pointz.in:9091/karza/epan"
  InhouseGetAndValidateEPANURL: "https://simulator.staging.pointz.in:9091/inhouse/epan"

  # ITR
  InhouseVerifyAndGetITRIntimationDetailsURL: "https://simulator.staging.pointz.in:9091/inhouse/verify/itr-intimation"

  #Call back urls for account setup
  CreateCustomerCallBackUrl: "https://vnotificationgw.staging.pointz.in/openbanking/customer/create"
  CreateAccountCallBackUrl: "https://vnotificationgw.staging.pointz.in/openbanking/account/create"

  # Liveness Service
  Veri5CheckLivenessRequestURL: "https://simulator.staging.pointz.in:9091/video-id-kyc/api/1.0/liveness"
  Veri5MatchFaceRequestURL: "https://sandbox.veri5digital.com/video-id-kyc/api/1.0/faceCompare"
  KarzaCheckLivenessRequestURL: "https://simulator.staging.pointz.in:9091/v3/uat/video-liveness"
  KarzaLivenessCallbackURL: "https://vnotificationgw.staging.pointz.in/liveness/karza"
  KarzaMatchFaceRequestURL: "https://simulator.staging.pointz.in:9091/v3/facesimilarity"
  KarzaCheckPassiveLivenessRequestURL: "https://simulator.staging.pointz.in:9091/v3/image-liveness"
  KarzaCheckLivenessStatusURL: "https://simulator.staging.pointz.in:9091/v3/video-liveness-status"
  InhouseCheckLivenessRequestURL: "https://simulator.staging.pointz.in:9091/inhouse-liveness"
  InhouseMatchFaceRequestURL: "https://simulator.staging.pointz.in:9091/inhouse-facematch"
  InhouseMatchFaceRequestURLV2: "https://simulator.staging.pointz.in:9091/inhouse-facematch"
  UseFormMarshalForKarza: false
  UseFormMarshalForKarzaFM: false

  # Employment Service
  Employment:
    KarzaPFOTPURL: "https://simulator.staging.pointz.in:9091/v2/epf-get-otp"
    KarzaPFPassbookURL: "https://simulator.staging.pointz.in:9091/v3/epf-get-passbook"
    KarzaEmploymentVerificationURL: "https://testapi.karza.in/v2/employment-verification-advanced"
    KarzaSearchCompanyNameURL: "https://testapi.kscan.in/v3/employer-search-lite"
    KarzaUANLookupURL: "https://simulator.staging.pointz.in:9091/v2/uan-lookup"
    KarzaEPFAuthURL: "https://testapi.karza.in/v2/epf-auth"
    KarzaEmployeeNameSearchURL: "https://testapi.karza.in/v2/employee-search"
    KarzaCompanyMasterLLPDataURL: "https://testapi.karza.in/v2/mca"
    KarzaSearchGSTINBasisPAN: "https://simulator.staging.pointz.in:9091/uat/v1/search"
    KarzaGetForm16QuarterlyURL: "https://testapi.karza.in/v3/tdsq"
    KarzaGetEmployerDetailsByGstinURL: "https://api.karza.in/gst/uat/v2/gst-verification"
    KarzaGetUANFromPan: "https://simulator.staging.pointz.in:9091/v2/uan-by-pan"
    SignzyLoginURL: "https://simulator.staging.pointz.in:9091/v2/login"
    SignzyDomainNameVerificationURL: "https://simulator.staging.pointz.in:9091/v2/domainverifications"
    KarzaFindUanByPan: "https://simulator.staging.pointz.in:9091/v3/pan-uan"
    TartanUpdateEmployeeBankDetailsURL: "https://tnode.tartanhq.com/api/update_bank/"

  # Freshdesk service
  FreshdeskAgentURL: "https://ficaretesting.freshdesk.com/api/v2/agents"
  FreshdeskTicketURL: "https://ficaretesting.freshdesk.com/api/v2/tickets"
  FreshdeskFilterTicketURL: "https://ficaretesting.freshdesk.com/api/v2/search/tickets"
  FreshdeskSolutionsURL: "https://ficaretesting.freshdesk.com/api/v2/solutions"
  FreshdeskContactsURL: "https://ficaretesting.freshdesk.com/api/v2/contacts"
  FreshdeskTicketFieldURL: "https://ficaretesting.freshdesk.com/api/v2/admin/ticket_fields"
  CxFreshdeskTicketAttachmentsBucketName: "epifi-staging-cx-ticket-attachments"

  # CRM Service
  FreshdeskURI:
    Agent: "/agents"
    Ticket: "/tickets"
    FilterTicket: "/search/tickets"
    Solutions: "/solutions"
    Contacts: "/contacts"
    TicketField: "/admin/ticket_fields"
    Job: "/jobs"
    BulkUpdate: "/tickets/bulk_update"
  FreshDeskAccountConfig:
    EPIFI_TECH:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifitechnologies.freshdesk.com/api/v2"
    FEDERAL_BANK:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifitechnologies.freshdesk.com/api/v2"

  #Freshchat service
  FreshchatConversationURL: "https://epifi6.freshchat.com/v2/conversations"
  FreshchatUserURL: "https://epifi6.freshchat.com/v2/users"
  FreshchatAgentURL: "https://epifi6.freshchat.com/v2/agents"


  InhouseNameCheckUrl: "https://simulator.staging.pointz.in:9091/namematch"
  InhouseEmployerNameMatchUrl: "https://entity-matcher.data-dev.pointz.in/api/v1/companymatch"
  InhouseEmployerNameCategoriserUrl: "http://text-semantics.data-dev.pointz.in/v1/name_categoriser"
  InhouseBreForCCUrl: "https://simulator.staging.pointz.in:9091/inhouseBre"

  DrivingLicenseValidationUrl: "https://simulator.staging.pointz.in:9091/v3/dl"

  VoterIdValidationUrl: "https://simulator.staging.pointz.in:9091/v2/voter"

  BankAccountVerificationUrl: "https://simulator.staging.pointz.in:9091/verify-bank-account"

  CAMS:
    OrderFeedFileURL: "https://simulator.staging.pointz.in:8080/cams/ProcessOrderFeedFile"
    FATCAFileURL: "https://simulator.staging.pointz.in:8080/cams/ProcessFATCAFeedFile"
    ElogFileURL: "https://simulator.staging.pointz.in:8080/cams/ProcessElogFile"
    OrderFeedFileStatusURL: "https://simulator.staging.pointz.in:8080/cams/GetOrderFeedFileStatus"
    OrderFeedFileSyncURL: "https://simulator.staging.pointz.in:8080/cams/ProcessOrderFeedFileSync"
    NFTFileURL: "https://simulator.staging.pointz.in:8080/cams/ProcessNFTFile"
    GetFolioDetailsURL: "https://simulator.staging.pointz.in:8080/cams/GetFolioDetails"
    S3Bucket: "epifi-staging-mutualfund"

  Tiering:
    AddSchemeChangeURL: "https://simulator.staging.pointz.in:9091/tiering/schemeChangeAdd"
    EnquireSchemeChangeURL: "https://simulator.staging.pointz.in:9091/tiering/schemeChangeEnq"

  SmallCase:
    CreateTransactionURL: "https://simulator.staging.pointz.in:8080/smallcase/CreateTransaction"
    InitiateHoldingsImportURL: "https://simulator.staging.pointz.in:8080/smallcase/InitiateHoldingsImportURL"
    TriggerHoldingsImportFetchURL: "https://simulator.staging.pointz.in:8080/smallcase/TriggerHoldingsImportFetchURL"
    MFAnalyticsURL: "https://simulator.staging.pointz.in:8080/smallcase/MfAnalytics"
    SmallCaseGateway: "fimoney-stag"

  MFCentral:
    GenerateTokenURL: "https://simulator.staging.pointz.in:8080/mfcentral/GenerateToken"
    EncryptAndSignURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
    VerifyAndDecryptURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
    UpdateFolioEmailURL: "https://simulator.staging.pointz.in:8080/mfcentral/api/client/v1/updateEmail"
    UpdateFolioMobileURL: "https://simulator.staging.pointz.in:8080/mfcentral/api/client/v1/updateMobile"
    InvestorConsentUrl: "https://simulator.staging.pointz.in:8080/mfcentral/api/client/v1/investorconsent"
    SubmitCasSummaryUrl: "https://simulator.staging.pointz.in:8080/mfcentral/api/client/v1/submitcassummaryrequest"
    GetCasDocumentUrl: "https://simulator.staging.pointz.in:8080/mfcentral/api/client/v1/getcasdocument"
    GetTransactionStatusUrl: "https://simulator.staging.pointz.in:8080/mfcentral/api/client/v1/getTransactionStatus"

  ## aa smart parser
  InHouseAAParserURL: "https://smart-parser.data-dev.pointz.in/parse"
  InHouseAABulkParserURL: "https://smart-parser.data-dev.pointz.in/bulk_parse"

  #SMS Service
  Exotel:
    URL: "https://%s:%<EMAIL>/v1/Accounts/%s/SMS"
    AccountSid: "epifi2"
    SenderId: "***********"
  Twilio:
    URL: "https://api.twilio.com/2010-04-01/Accounts/%s/Messages"
    SenderId: "***********"
  AclEpifi:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "epifsalt"
    SenderId: "FiMony"
  AclFederal:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "fbefistg"
    SenderId: "Fedfib"
  AclEpifiOtp:
    FallbackURL: "https://dmzotp.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    URL: "https://otp2.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    AppId: "epifiotp"
    SenderId: "FiMony"
  KaleyraFederal:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  KaleyraEpifi:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FiMony"
  KaleyraEpifiNR:
    URL: "https://api.in.kaleyra.io/v1/HXIN1778099997IN/messages"
    SenderId: "FIMONY"
    CallbackProfileId: "IN_921541db-1b15-4571-b5db-3aa26bb7cbd1"
  KaleyraSmsCallbackURL: "https://vnotificationgw.staging.pointz.in/sms/callback/kaleyra/UrlListner/requestListener"
  AclWhatsapp:
    URL: "https://pushuat.aclwhatsapp.com/pull-platform-receiver/wa/messages"
    OptInURL: "http://115.113.127.155:8085/api/v1/addoptinpost"
  KaleyraFederalCreditCard:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  #Loylty Service
  Loylty:
    AuthTokenURL: "https://authuat.loylty.com/mtoken"
    GiftCardBookingURL: "https://uategvb9.loylty.com/V2/GiftCard/Request"
    CharityBookingURL: "https://uatcbkb9.loylty.com/V2/InitiateV2"
    GiftCardProductListURL: "https://uategvb9.loylty.com/V2/GiftCard/Products"
    GiftCardProductDetailURL: "https://uategvb9.loylty.com/V2/GiftCard/Products/%s"
    CreateOrderURL: "https://uatordb9.loylty.com/V2/Order"
    ConfirmOrderURL: "https://uatordb9.loylty.com/V2/Order/%s/Confirm"
    GetOrderDetailsURL: "https://uatordb9.loylty.com/V2/Order/%s"

  Qwikcilver:
    GetAuthorizationCodeBaseUrl: "https://simulator.staging.pointz.in:8080/offers/qwikcilver/authorization-code"
    GetAccessTokenBaseUrl: "https://simulator.staging.pointz.in:8080/offers/qwikcilver/access-token"
    CreateOrderBaseUrl: "https://simulator.staging.pointz.in:8080/offers/qwikcilver/create-order"
    GetActivatedCardDetailsBaseUrl: "https://simulator.staging.pointz.in:8080/offers/qwikcilver/activated-card-details/%s"
    GetCategoryDetailsBaseUrl: "https://simulator.staging.pointz.in:8080/offers/qwikcilver/category-details"
    GetOrderStatusBaseUrl: "https://simulator.staging.pointz.in:8080/offers/qwikcilver/order-status/%s"
    GetProductDetailsBaseUrl: "https://simulator.staging.pointz.in:8080/offers/qwikcilver/product-details/%s"
    GetProductListBaseUrl: "https://simulator.staging.pointz.in:8080/offers/qwikcilver/product-list/%s"
    AccessTokenValidityDuration: "5s"
    MailOrderDetailsTo: "<EMAIL>"

  MoEngage:
    BaseUrl: "https://api-03.moengage.com/v1"

  Thriwe:
    BaseUrl: "https://staging-india-api-gateway.thriwe.com"

  Riskcovry:
    BaseUrl: "https://api.uat-riskcovry.com/api/partner"

  Onsurity:
    BaseUrl: "https://simulator.staging.pointz.in:8080"

  Karvy:
    KarviAppId: "RIA"
    AgentCode: "INA200015185"
    BranchCode: "R999"
    BucketName: "epifi-staging-mutualfund-karvy"
    FATCAFileURL: "https://simulator.staging.pointz.in:8080/karvy/ProcessFATCAFeedFile"
    OrderFeedFileSyncURL: "https://simulator.staging.pointz.in:8080/karvy/ProcessOrderFeedFileSync"
    OrderFeedFileV2SyncURL: "https://simulator.staging.pointz.in:8080/karvy/ProcessOrderFeedFileSync"
    NFTFileUploadURL: "https://simulator.staging.pointz.in:8080/karvy/NFTFileUploadURL"
    GetFolioDetailsURL: "https://simulator.staging.pointz.in:8080/karvy/GetFolioDetailsURL"

  Razorpay:
    BaseUrl: "https://api.razorpay.com"

  #json file path
  PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
  PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
  PayAckStatusCodeJson: "./mappingJson/ackStatusCode.json"

  DepositAckStatusCodeFilePath: "./mappingJson/depositAckStatusCode.json"
  DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"

  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  SIResponseStatusCodeFilePath: "./mappingJson/siResponseStatusCodes.json"

  Federal:
    CheckCustomerStatusForNonResidentURL: "https://simulator.staging.pointz.in:8080/checkCustomerStatusForNonResident"
    CreateCustomerForNonResidentURL: "https://simulator.staging.pointz.in:8080/createCustomerForNonResident"
    CustomerDetailsInsertURL: "https://simulator.staging.pointz.in:8080/upgradeKycLevel"
    PanValidationV2Url: "https://simulator.staging.pointz.in:9091/fedbnk/pan/v2.0.0/validate"
    PayIntraBankURL: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/federal/intraBank"
    PayNEFTURL: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/federal/neft"
    PayIMPSURL: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/federal/imps"
    PayRTGSURL: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/federal/rtgs"
    PayStatusURL: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/federal/enquiry"
    PayIntraBankDepositURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/OwnDepositAccFT"
    RemitterDetailsFetchUrl: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/federal/GetRemitterDetails"
    RemitterDetailsV1FetchUrl: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/federal/GetRemitterDetailsV1"
    BeneficiaryNameLookupUrl: "https://simulator.staging.pointz.in:9091/openbanking/account_utility/v1.0.0/BenefiAcctNameLookup"
    GetCsisStatusUrl: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/federal/CSISStatusCheck"

    PayIntraBankCallbackURL: "https://vnotificationgw.staging.pointz.in/openbanking/payment/federal"
    PayNEFTCallbackURL: "https://vnotificationgw.staging.pointz.in/openbanking/payment/federal"
    PayIMPSCallbackURL: "https://vnotificationgw.staging.pointz.in/openbanking/payment/federal"
    PayRTGSCallbackURL: "https://vnotificationgw.staging.pointz.in/openbanking/payment/federal"

    InquireOrReportGSTCollectionURL: "https://simulator.staging.pointz.in:8080/test/federal/InquireOrReportGSTCollection"
    InquireOrReportGSTCollectionChannelId: "epifi"

    # B2C Payments
    PayB2CIntraBankURL: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/B2C/federal/intraBank"
    PayB2CIntraBankCallbackURL: "https://vnotificationgw.staging.pointz.in/openbanking/payment/b2c/federal"
    PayB2CStatusURL: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/B2C/federal/enquiry"
    PayB2CImpsURL: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/B2C/federal/imps"
    PayB2CImpsCallbackURL: "https://vnotificationgw.staging.pointz.in/openbanking/payment/b2c/federal"

    # Shipping Preference Service
    ShippingAddressUpdateURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/ShipAddrModification"
    ShippingAddressUpdateCallbackURL: "https://vnotificationgw.staging.pointz.in/openbanking/shipping_preference/federal"

    # Card Service
    DebitCardCreateURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardCreation"
    DebitCardCreateCallbackURL: "https://vnotificationgw.staging.pointz.in/openbanking/card/federal"
    DebitCardActivateURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardActivation"
    DebitCardEnquiryUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardEnqService"
    DebitCardPinSetUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinChangeUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinResetUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinValidationUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardBlockUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardSuspendOnOffUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardLocationOnOffUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardECommerceOnOffUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardCVVEnquiryUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/CVVEnquiry"
    DebitCardLimitEnquiry: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/LimitEnquiry"
    DebitCardUpdateLimit: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/LimitUpdate"
    DebitCardDeliveryTracking: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/DeliveryTracking"
    DebitCardConsolidatedCardControlUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/FHMCardONOFF"
    DebitCardPhysicalDispatchUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking-card/cardDispatch"
    DebitCardPhysicalDispatchCallbackUrl: "https://vnotificationgw.staging.pointz.in/openbanking/cardPhysicalDispatch/federal"
    CheckDebitCardIssuanceFeeStatusUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcationStatus"
    DebitCardCollectIssuanceFeeUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcation"

    # PAN Service
    PANValidationURL: "https://simulator.staging.pointz.in:8080/panValidation"
    PANAadhaarValidationURL: "https://simulator.staging.pointz.in:9091/panAadhaarValidation"

    EkycNameDobValidationURL: "https://simulator.staging.pointz.in:8080/ekyc/namedob/validation"
    AadharMobileValidationURL: "https://simulator.staging.pointz.in:9091/aadharmobilevalidate"
    ShareDocWithVendorURL: ""

    # UN Name Check Service
    UNNameCheckURL: "https://simulator.staging.pointz.in:8080/UNNameCheck"

    # Device Re-registration / Profile Update API
    DeviceReRegURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/device/re-registration"
    DeviceReRegCallbackUrl: "https://vnotificationgw.staging.pointz.in/openbanking/auth/federal/user-device/re-register"

    # Device De-registration / Deactivate User
    DeviceDeRegURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/user-device/deactivation"

    # Generate OTP
    GenerateOTPURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/auth/generate-otp"

    DeviceReactivationURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/user-device/reactivate"
    # Deposit service
    CreateFDURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/CreateFD"
    CreateSDURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/CreateSD"
    CreateRDURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/CreateRD"
    AutoRenewFdURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/AutoRenewFd"
    CloseDepositAccountURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/ClosingDepositAcc"
    CheckDepositAccountStatusURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/DepositEnq"
    GetDepositAccountDetailURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/GetAccDetails"
    GetPreClosureDetailURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/digitalCredit/v1.0.0/DepositAcctPreclosEnq"
    DepositListAccountURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/GetAccList"
    InterestRateInfoURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/interestRateInfo"
    CalculateInterestDetailsURL: "https://simulator.staging.pointz.in:9091/fedbnk/uat/neobanking/CalculateInterestDetailsURL"
    CreateDepositCallbackUrl: "https://vnotificationgw.staging.pointz.in/openbanking/deposit/federal/create"
    PreCloseDepositCallbackUrl: "https://vnotificationgw.staging.pointz.in/openbanking/deposit/federal/preclose"
    AutoRenewFdCallbackUrl: "https://vnotificationgw.staging.pointz.in/openbanking/deposit/federal/AutoRenewFd"

    # Standing Instruction service
    CreateSIUrl: "https://simulator.staging.pointz.in:9091/standinginstruction/federal/sicreate"
    ExecuteSIUrl: "https://simulator.staging.pointz.in:9091/standinginstruction/federal/siexecute"
    SICallbackUrl: "https://vnotificationgw.staging.pointz.in/openbanking/payment/federal"
    ModifySIUrl: "https://simulator.staging.pointz.in:9091/standinginstruction/federal/simodify"
    RevokeSIUrl: "https://simulator.staging.pointz.in:9091/standinginstruction/federal/sirevoke"

    # csv file path
    CityCodesCsv: "./mappingCsv/cityCodes.csv"
    StateCodesCsv: "./mappingCsv/stateCodes.csv"
    CountryCodesCsv: "./mappingCsv/countryCodes.csv"

    #Account
    OpeningBalanceURL: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/GetAccStatement"
    ClosingBalanceURL: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/GetClosingBalance"
    AccountStatementURL: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/GetAccStatement"
    AccountStatementByDRApiUrl: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/GetAccStatementByDrApi"
    EnquireBalanceV1URL: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/GetBalanceV1"
    MiniStatementURL: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/GetMiniStatement"
    AccountStatusURL: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/AccountStatusEnquiry"
    ThirdPartyAccountCollectionURL: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/tpAccountCollection"
    UpdateNomineeUrl: "https://simulator.staging.pointz.in:9091/openbanking/accounts/federal/nomineeUpdate"

    # Partner SDK
    GetSessionParamsUrl: "https://simulator.staging.pointz.in:9091/fedbnk/uat/partnersdk/GetSessionParams"

    # Enquiry Service Url
    CustomerCreationEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/CustomerCreationEnquiryStatus"
    AccountCreationEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/AccountCreationEnquiryStatus"
    CardCreationEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/CardCreationEnquiryStatus"
    DeviceReRegistrationDetailsEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/DeviceReRegistrationDetailsEnquiryStatus"
    MailingAddressModifyDetailsEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/MailingAddressModifyDetailsEnquiryStatus"
    ShippingAddressUpdateEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/ShippingAddressUpdateEnquiryStatus"
    DeviceRegistrationDetailsEnquiryStatusURL: "https://simulator.staging.pointz.in:9091/DeviceRegistrationDetailsEnquiryStatus"
    PhysicalCardDispatchDetailsEnquiryStatus: "https://simulator.staging.pointz.in:9091/PhysicalCardDispatchDetailsEnquiryStatus"

    # Chequebook Request and Track URLs
    OrderChequebookUrl: "https://simulator.staging.pointz.in:9091/fedbnk/neobanking/v1.0.0/chequeBookOrder"
    TrackChequebookUrl: "https://simulator.staging.pointz.in:9091/fedbnk/neobanking/v1.0.0/chequeBookTrack"
    IssueDigitalCancelledChequeUrl: "https://simulator.staging.pointz.in:9091/fedbnk/account_utility/v1.0.0/digitalChequeLeafIssuance"

    # Profile Update and enquiry URL
    ProfileUpdateUrl: "https://simulator.staging.pointz.in:9091/fedbnk/account_utility/v1.0.0/updateProfileAtBank"
    ProfileUpdateEnquiryUrl: "https://simulator.staging.pointz.in:9091/fedbnk/account_utility/v1.0.0/profileUpdateStatus"

    # lien service url
    LienUrl: "https://simulator.staging.pointz.in:9091/openbanking/lien/federal"
    TcsCalculationURL: "https://simulator.staging.pointz.in:8080/test/federal"
    TcsCalculationChannelId: "EPI"

    # e-nach service url
    ListEnachUrl: "https://simulator.staging.pointz.in:9091/NACHEnquiry_API/v1/enquiry"

    FetchEnachTransactionsUrl: "https://simulator.staging.pointz.in:9091/digitalCredit/v1.0.0/enachdata"

  LeadSquared:
    CreateOrUpdateLeadUrl: "https://simulator.staging.pointz.in:8080/salaryprogram/leadsquared/CreateOrUpdateLead%s%s"

  Karza:
    GenerateSessionTokenUrl: "https://simulator.staging.pointz.in:8080/v3/get-jwt"
    AddNewCustomerUrl: "https://app.karza.in/test/videokyc/api/v2/customers"
    UpdateCustomerV3Url: "https://simulator.staging.pointz.in:8080/test/videokyc/api/v3/customers"
    AddNewCustomerV3Url: "https://simulator.staging.pointz.in:8080/test/videokyc/api/v3/customers"
    GenerateCustomerTokenUrl: "https://simulator.staging.pointz.in:8080/test/videokyc/api/v2/generate-usertoken"
    GetSlotUrl: "https://app.karza.in/test/videokyc/api/v2/get-slot"
    BookSlotUrl: "https://app.karza.in/test/videokyc/api/v2/book-slot"
    GenerateWebLinkUrl: "https://simulator.staging.pointz.in:8080/test/videokyc/api/v2/link"
    SlotAgentsUrl: "https://app.karza.in/test/videokyc/api/v2/slot-agents"
    TransactionStatusEnquiryUrl: "https://simulator.staging.pointz.in:8080/test/videokyc/api/v2/transaction-events"
    ReScheduleSlotUrl: "https://app.karza.in/test/videokyc/api/v2/reschedule-customer"
    TriggerCallback: "https://app.karza.in/test/videokyc/api/v2/trigger-callback"
    AgentDashboardUrl: "https://simulator.staging.pointz.in:8080/test/videokyc/api/agent-dashboard"
    AgentDashboardAuthUrl: "https://simulator.staging.pointz.in:8080/test/videokyc/api/agent-dashboard-auth"
    EmploymentVerificationAdvancedUrl: "https://simulator.staging.pointz.in:9091/karza/employmentVerificationAdvanced"
    KycOcrUrl: "https://simulator.staging.pointz.in:9091/karza/v1/extract_passport"
    PassportVerificationURL: "https://simulator.staging.pointz.in:9091/karza/v1/verify_passport"

  Roanuz:
    GenerateCricketAccessTokenUrl: "https://api.sports.roanuz.com/v5/core"
    CricketURL: "https://api.sports.roanuz.com/v5/cricket"
    GenerateFootballAccessTokenUrl: "https://api.footballapi.com/v1/auth"
    FootballUrl: "https://api.footballapi.com/v1"
    FootballAppId: "com.epififootball.www"
    FootballDeviceId: "developer"

  IPStack:
    GetLocationDetailFromIpUrl: "https://simulator.staging.pointz.in:8080/test/ipstack"

  CvlKra:
    SoapHost: "https://krapancheck.cvlindia.com"
    PanEnquiryURL: "https://simulator.staging.pointz.in:8080/CVLPanInquiry.svc"
    InsertUpdateKycURL: "https://simulator.staging.pointz.in:8080/CVLPanInquiry.svc"
    # CvlKra sftp config
    Host: "sftp.deploy.pointz.in"
    Port: 22

  NsdlKra:
    PanInquiryURL: "https://simulator.staging.pointz.in:8080/TIN/PanInquiryBackEnd"
    GenerateSignatureURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/nsdl/v1/generateSignature"
    DisableSigning: true
    PerformPanInquiryV4URL: "https://simulator.staging.pointz.in:8080/TIN/PanInquiryBackEnd"

  Manch:
    TransactionsURL: "https://simulator.staging.pointz.in:8080/app/api/transactions"
    DocumentsURL: "https://simulator.staging.pointz.in:8080/app/api/documents"
    OrgId: "TST00180"
    ReturnUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

  Digio:
    TransactionsURL: "https://ext.digio.in:444/v2/client/document"
    ExpiryInDays: 10

  WealthKarza:
    OcrURL: "https://testapi.karza.in/v3/kycocr"
    MaskAadhaar: true
    HideAadhaar: true
    Confidence: true
    CheckBlur: true
    CheckBlackAndWhite: true
    CheckCutCard: true
    CheckBrightness: true

  Experian:
    CheckCreditReportPresenceURL: "https://simulator.staging.pointz.in:8080/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportURL: "https://simulator.staging.pointz.in:8080/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportForExistingUserURL: "https://simulator.staging.pointz.in:8080/ECV-P2/content/onDemandRefresh.action"
    FetchExtendSubscriptionURL: "https://simulator.staging.pointz.in:8080/ECV-P2/content/consumerConsentReRegistration.action"

  Cibil:
    PingUrl: "https://simulator.staging.pointz.in:9091/cibil/consumer/dtc/ping"
    FulfillOfferUrl: "https://simulator.staging.pointz.in:9091/cibil/consumer/dtc/fulfilloffer"
    GetAuthQuestionsUrl: "https://simulator.staging.pointz.in:9091/cibil/consumer/dtc/GetAuthenticationQuestions"
    VerifyAuthAnswersUrl: "https://simulator.staging.pointz.in:9091/cibil/consumer/dtc/VerifyAuthenticationAnswers"
    GetCustomerAssetsUrl: "https://simulator.staging.pointz.in:9091/cibil/consumer/dtc/GetCustomerAssets"
    GetProductTokenUrl: "https://simulator.staging.pointz.in:9091/cibil/consumer/dtc/GetProductWebToken"
    ProductUrlPrefix: "https://atlasls-in-live.sd.demo.truelink.com/CreditView"

  Shipway:
    BulkUploadShipmentDataUrl: "https://simulator.staging.pointz.in:8080/shipway/BulkPushOrderData"
    GetShipmentDetailsUrl: "https://simulator.staging.pointz.in:9091/shipway/GetOrderShipmentDetails"
    AddOrUpdateWebhookUrl: "https://simulator.staging.pointz.in:9091/shipway/AddOrUpdateWebhook"
    UploadShipmentDataUrl: "https://simulator.staging.pointz.in:9091/shipway/PushOrderData"

  # AA service vendor URLs
  AA:
    BaseURL: "https://simulator.staging.pointz.in:8080"
    PostConsentURL: "/Consent"
    ConsentStatusURL: "/Consent/handle"
    ConsentArtefactURL: "/Consent"
    RequestDataURL: "/FI/request"
    FetchDataURL: "/FI/fetch"
    GetAccountLinkStatusURL: "/Account/link/status"
    GenerateAccessTokenURL: "https://api.uat.sahamati.org.in/iam/v1/entity/token/generate"
    FetchCrEntityDetailURL: "https://uatcr.sahamati.org.in/entityInfo/AA"
    FetchCrEntityDetailURLV2: "https://uatcr.sahamati.org.in/v2/entityInfo/AA"
    ConsentUpdateURL: "/consent/update"
    AccountDeLinkURL: "/account/delink"
    UseSahamatiCrAndToken: false
    OneMoneyCrId: "onemoney-aa"
    FinvuCrId: "<EMAIL>"
    GetAccountLinkStatusBulkURL: "/Account/link/Status"
    GetHeartbeatStatusURL: "/Heartbeat"
    EpifiAaKid: "654024c8-29c8-11e8-8868-0289437bf331"
    AAClientApiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************.A-VX3lgu6T_r2FWIp2bsDAQK9vll6p4uQC_D5LwXmdo"
    SahamatiClientId: "EPIFIUAT"
    GenerateFinvuJwtTokenURL: "/web/token"
    GetBulkConsentRequestURL: "/Consent/status/bulk"
    AaSecretsVersionToUse: "V1"
    FinvuFipMetricsURL: "/fip/latest-metrics-all"
    Ignosis:
      Url: "https://simulator.staging.pointz.in:8080"

  # Bouncy castle library URLs
  BouncyCastle:
    GenerateKeyPairURL: "https://simulator.staging.pointz.in:8080/ecc/v1/generateKey"
    GetSharedSecretURL: "https://simulator.staging.pointz.in:8080/ecc/v1/getSharedKey"
    DecryptDataURL: "https://simulator.staging.pointz.in:8080/ecc/v1/decrypt"
    CkycEncryptAndSignURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/ckyc/v1/encryptAndSign"
    CkycVerifyAndDecryptURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/ckyc/v1/verifyAndDecrypt"

  # Fennel Vendor URLs
  FennelFeatureStore:
    ExtractFeatureSetsURL: "https://main.epifi-staging.aws.fennel.ai/api/v1/extract_features"
    ExtractFeatureSetsURLV2: "https://epifi-staging.aws.fennel.ai/api/v1/query"
    ExtractFeatureSetsURLV3: "https://babel.data-dev.pointz.in/api/v1/query"
    LogDatasetsURL: "https://main.epifi-staging.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV2: "https://epifi-staging.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV3: "https://babel.data-dev.pointz.in/api/v1/log"
    PdScoreURL: "https://loan-default.data-dev.pointz.in/v2/loan_default"
    Simulator:
      Enable: true
      ExtractFeatureSetsURL: "https://simulator.staging.pointz.in:8080/fennel/api/v1/extract_features"
      LogDatasetsURL: "https://simulator.staging.pointz.in:8080/fennel/api/v1/log"
      AllowedWorkflowsForSimulation: [ "acquisition" ]

  # Scienaptic Vendor Config
  Scienaptic:
    GenerateSmsFeaturesURL: "https://simulator.staging.pointz.in:9091/scienaptic/scraper/generate_feature"

  Ckyc:
    SearchURL: "https://simulator.staging.pointz.in:8080/Search/ckycverificationservice/verify"
    ApiVersion: "1.2"
    DownloadURL: "https://simulator.staging.pointz.in:8080/Search/ckycverificationservice/download"
    EnableCryptor: false

  InhouseOCR:
    MaskDocURL: "https://ocular.data-dev.pointz.in/v1/mask_doc"
    ExtractFieldsURL: "https://ocular.data-dev.pointz.in/v1/extract_fields"
    DetectDocumentURL: "https://ocular.data-dev.pointz.in/v1/detect_doc"
    ExtractFieldsURLV2: "https://simulator.staging.pointz.in:9091/inhouse/v1/extract_fields"

  InhousePopularFAQUrl: "http://popular-faqs.data-dev.pointz.in"

  Digilocker:
    GetAuthorizationCodeUrl: "https://simulator.staging.pointz.in:8080/public/oauth2/1/authorize"
    GetAccessTokenUrl: "https://simulator.staging.pointz.in:8080/public/oauth2/1/token"
    ClientId: "EBB0DE86"
    RedirectUri: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"
    GetListOfIssuedDocumentsUrl: "https://simulator.staging.pointz.in:8080/public/oauth2/2/files/issued"
    GetRefreshTokenUrl: "https://simulator.staging.pointz.in:8080/public/oauth2/1/token"
    GetFileFromUriUrl: "https://simulator.staging.pointz.in:8080/public/oauth2/1/file/"
    GetAadhaarInXmlUrl: "https://simulator.staging.pointz.in:8080/public/oauth2/3/xml/eaadhaar"
    TokenCacheTtl: 10m

  Liquiloans:
    Host: "https://simulator.staging.pointz.in:9091"
    SupplyIntegrationHost: "https://simulator.staging.pointz.in:9091"
    SftpHost: "sftp.deploy.pointz.in"
    SftpPort: 22
    S3BucketP2PInvestmentLedger: "epifi-staging-p2p-investment-ledger"

  SetU:
    PartnerId: "1510324753200579781"
    BaseURL: "https://sandbox-coudc.setu.co" # using UAT Url in staging for testing. todo: update simulator apis
    MobileRechargeProductInstanceId: "8eea3ab4-09c4-4d97-824c-7f93aa902c08"
    MobileRechargeLoginBaseUrl: "https://accountservice.setu.co" # todo: update to simulator apis
    MobileRechargeBaseUrl: "https://prepaid-uat.setu.co" # todo: update to simulator apis

  # TODO(@prasoon): update URL once available
  Lending:

    PreApprovedLoan:
      Federal:
        UrlLentra: "https://simulator.staging.pointz.in:8080/lending/federal/v1/enquiry"
        Url: "https://simulator.staging.pointz.in:9091"
        HttpUrl: "https://simulator.staging.pointz.in:8080"
        FetchDetailsUrl: "https://simulator.staging.pointz.in:9091/account_utility/v1.0.0/fetchLoanDetails"
        RespUrl: ""
        # TODO(@kantikumar): update sftp host once available
        SftpHost: ""
        SftpPort: 22
        PlAcntCrnNtbHttpURL: "https://simulator.staging.pointz.in:8080/loan/account/v2.0.0/creation"
        PlAcntCrnEnqNtbHttpURL: "https://simulator.staging.pointz.in:8080/loan/v1.0.0/enquiry"
      Liquiloans:
        Url: "https://simulator.staging.pointz.in:9091"
        # This URL points to HTTP port of simulator as few Liquiloans API needs request body  in HTTP GET request, those are
        # mocked using Router and not by gRPC method.
        HttpUrl: "https://simulator.staging.pointz.in:8080"
      Idfc:
        Url: "https://simulator.staging.pointz.in:9091"
        # URL to fetch the access token for IDFC APIs
        GetAccessTokenUrl: "https://app.uat-opt.idfcfirstbank.com/platform/oauth/oauth2/token"
        MandatePageUrl: "https://simulator.staging.pointz.in:8080/IDFCEMandate/EMandateB2BPaynimmo.aspx"
        EnableEncryption: false
        Source: "FIMONEY"
        SimulatorHttpURL: "https://simulator.staging.pointz.in:8080"
      Abfl:
        Url: "https://simulator.staging.pointz.in:9091"
        BreUrl: "https://simulator.staging.pointz.in:9091/v2/decisionEngineConfig"
        TxnDetailsUrl: "https://simulator.staging.pointz.in:9091"
        PwaJourneyUrl: "https://simulator.staging.pointz.in:9091/abfl/pwa"
      Moneyview:
        BaseUrl: "https://simulator.staging.pointz.in:9091/moneyview"
        # This URL points to HTTP port of simulator as few MV API needs request body  in HTTP GET request, those are
        # mocked using Router and not by gRPC method.
        HttpUrl: "https://simulator.staging.pointz.in:8080/moneyview"
      Setu:
        BaseUrl: "https://simulator.staging.pointz.in:9091"
      Digitap:
        UanAdvancedUrl: "https://simulator.staging.pointz.in:9091/v3/uan_advanced/sync"
      Lenden:
        ProductId: "EcoX-Loan-102"
        # Simulated non-prod env config
        BaseUrl: "https://simulator.staging.pointz.in:9091/lenden/common/v1/EPF"
        EnableCryptor: false
        # For remote-debug testing
#        BaseUrl: "https://localhost:9091/lenden/common/v1/EPF"
#        EnableCryptor: false
        # Vendor's non-prod env config
#        BaseUrl: "https://dev-tsp-gateway.lendenclub.com/v1/EPF/"
#        EnableCryptor: true

    CreditCard:
      M2P:
        RegisterCustomerHost: "https://simulator.staging.pointz.in:9091/"
        M2PHost: "https://simulator.staging.pointz.in:9091/"
        CreditCardRepaymentHost: "https://simulator.staging.pointz.in:9091/"
        M2PFallbackHost: "https://simulator.staging.pointz.in:9091/"
        M2PLMSHost: "https://simulator.staging.pointz.in:9091/"
        M2PPartnerSdkUrl: "https://simulator.staging.pointz.in:9091/gateway"
        M2PSetPinUrl: "https://simulator.staging.pointz.in:9091/"
        EnableEncryption: false
        M2PFederalHost: "https://simulator.staging.pointz.in:9091/"
      Federal:
        Url: "https://simulator.staging.pointz.in:9091/"
        UpdateCreditCardLimitDetailsUnsecuredChannel: "M2P-FDEPIFICR"
        UpdateCreditCardLimitDetailsMassUnsecuredChannel: "M2P-FDEPIFIMASSCR"
    CreditLine:
      M2P:
        Url: "https://simulator.staging.pointz.in:9091"
      Federal:
        Url: "https://simulator.staging.pointz.in:9091/limitFetch"
    Collateral:
      LendingMFCentralConfig:
        HttpUrl:
          AuthToken: "https://simulator.staging.pointz.in:9091/mfcentral/oauth/token"
          EncryptAndSign: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
          DecryptAndVerify: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
          SubmitCasSummary: "https://simulator.staging.pointz.in:9091/mfcentral/submitcassummary"
          InvestorConsent: "https://simulator.staging.pointz.in:9091/mfcentral/investorconsent"
          GetCasDocument: "https://simulator.staging.pointz.in:9091/mfcentral/getcasdocument"
          ValidateLien: "https://simulator.staging.pointz.in:9091/mfcentral/validatelien"
          SubmitLien: "https://simulator.staging.pointz.in:9091/mfcentral/submitlien"
          InvokeRevokeLien: "https://simulator.staging.pointz.in:9091/mfcentral/validateLienInvokeRevoke"
          CheckStatus: "https://simulator.staging.pointz.in:9091/mfcentral/lienCheckStatus"
          GetTransactionStatus: "https://simulator.staging.pointz.in:9091/mfcentral/getTransactionStatus"
    SecuredLoans:
      Url: "https://simulator.staging.pointz.in:8080/fiftyfin"

  Alpaca:
    BrokerApiHost: "https://simulator.staging.pointz.in:8080/test/alpaca"
    BrokerApiVersion: "v1"
    MarketApiHost: "https://data.sandbox.alpaca.markets"
    MarketApiVersion: "v2"
    StreamApiHost: "simulator.staging.pointz.in:8080"
    StreamApiPath: "test/v2/iex"
    StreamApiScheme: "wss"
    ShouldUseSimulatedEnvForWs: true
    OrderEventsApiPath: "/test/alpaca/v1/trade/events"
    AccountEventsApiPath: "/test/alpaca/v1/account/events"
    FundTransferEventsPath: "/test/alpaca/v1/events/transfers/status"
    JournalEventsPath: "/test/alpaca/v1/events/journals/status"
    BrokerEventsApiHost: "simulator.staging.pointz.in:8080"
    BrokerEventsApiScheme: "https"
    ShouldUseSimulatedEnvForEvents: true
    MarketDataBetaAPIPrefix: "v1beta1"

  MorningStar:
    TokenCacheTtl: 12h
    EquityAPIURL: "https://equityapi.morningstar.com"
    ApiUrl: "https://api.morningstar.com"
    ApiAccessTokenExpiryInDays: 90
    MorningStarObsoleteFundAPIUrl: "https://intools.morningstar.com/identifier/api/data"

  FederalInternationalFundTransfer:
    URL: "https://simulator.staging.pointz.in:8080/test/federal"
    CheckLRSEligibilityPrecision: 9

  Esign:
    Leegality:
      Url: "https://simulator.staging.pointz.in:9091/"

  ProfileValidation:
    Federal:
      Url: "https://simulator.staging.pointz.in:9091"

  #GPlace Vendor URLs
  GPlace:
    FindPlaceReqURL: "https://maps.googleapis.com/maps/api/place/findplacefromtext/json"
    GetPlaceDetailsReqURL: "https://maps.googleapis.com/maps/api/place/details/json"

  GoogleReverseGeocodingUrl: "https://simulator.staging.pointz.in:9091/get-address-coordinate"
  GoogleGeocodingUrl: "https://simulator.staging.pointz.in:9091/get-coordinate-for-address"
  InhouseLocationServiceUrl: "https://geo.data-dev.pointz.in"
  MaxmindIp2CityUrlPrefix: "https://simulator.staging.pointz.in:9091/get-address-ip/"

  BureauPhoneNumberDetailsUrl: "https://simulator.staging.pointz.in:9091/v1/phone-network"

  #DronaPay
  DronapayHostURL: "https://riskuat.dronapay.pointz.in/springapi"

  InhouseRiskServiceURL: "https://onboarding-risk-detection.data-dev.pointz.in/v3/onboarding_risk_detection"
  InhouseRiskServiceURLV1: "https://onboarding-risk-detection.data-dev.pointz.in/v1/shadow_onboarding_risk_detection"
  InhouseReonboardingRiskServiceURL: "https://onboarding-risk-detection.data-dev.pointz.in/v1/afu_risk_detection"
  CasePrioritisationModel:
    InHouseUrl: "https://ds-wise-prioritise.data-dev.pointz.in/prioritisation/v0"

  "InhouseMerchantResolutionServiceUrl": "https://merchant.data-dev.pointz.in/resolution"

  Aml:
    Tss:
      Epifi:
        ScreeningUrl: "https://simulator.staging.pointz.in:8080/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"
        SystemName: "Epifi_tech"
        ParentCompany: "Epifi"
      StockGuardian:
        ScreeningUrl: "https://simulator.staging.pointz.in:8080/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"
        SystemName: "LMS"
        ParentCompany: "SGIPL"
    TSSCloud:
      # Simulator setup
#      EnableCryptor: false
#      Tenants:
#        OWNER_EPIFI_TECH:
#          Name: "EPIFITech"
#          URL: "https://simulator.staging.pointz.in:8080"
#          # Use localhost for remote-debug style testing with simulator changes
#          # URL: "https://localhost:8080"
#        OWNER_STOCK_GUARDIAN_TSP:
#          Name: "LMS"
#          URL: "https://simulator.staging.pointz.in:8080"
#          # Use localhost for remote-debug style testing with simulator changes
#          # URL: "https://localhost:8080"
      # UAT setup
       EnableCryptor: true
       Tenants:
         OWNER_EPIFI_TECH:
           Name: "EPIFITech"
           URL: "https://epifitechnologiespvtltd-sb.trackwizz.app"
         OWNER_STOCK_GUARDIAN_TSP:
           Name: "LMS"
           URL: "https://stockguardiansindiapvtltd-sb.trackwizz.app"

  IncomeEstimatorConf:
    InhouseIncomeEstimatorURL: "https://simulator.staging.pointz.in:9091/get-income-estimate"

  LocationModel:
    InHouseUrl: "https://simulator.staging.pointz.in:9091/risk/location"
    RiskSeverityValToEnumMapping:
      "LOW": "RISK_SEVERITY_LOW"
      "MEDIUM": "RISK_SEVERITY_MEDIUM"
      "HIGH": "RISK_SEVERITY_HIGH"
      "CRITICAL": "RISK_SEVERITY_CRITICAL"

  EpanConfig:
    FederalConfig:
      SftpConn:
        Host: "sftp.deploy.pointz.in"
        Port: 22

  EnachConfig:
    FederalConfig:
      SftpConn:
        Host: "sftp.deploy.pointz.in"
        Port: 22
      FederalSFTPUploadPath: "/data/"
      FederalSFTPDownloadPath: "/data/"
      FileTypeToSFTPPathMap:
        FILE_TYPE_PRESENTATION_FILE: "/data/"
        FILE_TYPE_PRESENTATION_ACK_FILE: "/data/"
        FILE_TYPE_PRESENTATION_RESPONSE_FILE: "/data/"

  Dreamfolks:
    BaseUrl: "https://simulator.staging.pointz.in:9091/dreamfolks"
    ProgramId: "1000001632"
    ServiceId: "11"

  Visa:
    FindNearbyAtmTotalsUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/totalsinquiry"
    FindNearbyAtmsUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/atmsinquiry"
    FindGeocodesUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/geocodesinquiry"
    GetEnhancedForeignExchangeRatesUrl: "https://sandbox.api.visa.com/fx/rates"
    GetEnhancedMarkupForeignExchangeRatesUrl: "https://sandbox.api.visa.com/fx/rates/markup"

  Saven:
    CreditCardBaseUrl: "https://simulator.staging.pointz.in:9091/cc-saven"
    JwtExpiry: "3s"

  PerfiosDigilocker:
    ApiHost: "https://api-in-uat.perfios.com/kyc/api/v1/digilocker"

  Bridgewise:
    ApiHost: "https://rest.bridgewise.com"
    TokenCacheTtl: 12h

  FederalEscalation:
    BaseURL: "https://uatgateway.federalbank.co.in"
    CreateEscalationURL: "fedbnk/uat/CRM/v1.0.0/cxiosrcreation"
    BulkFetchURL: "fedbnk/uat/CRM/v1.0.0/cxsrbulkretrieval"
    S3BucketName: "epifi-staging-cx-ticket-attachments"

  Nugget:
    BaseURL: "https://api.nugget.com"
    AccessTokenEndpoint: "/unified-support/auth/users/getAccessToken"
    BusinessId: 1

Server:
  Ports:
    GrpcPort: 8081
    GrpcSecurePort: 9522
    HttpPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    # Payment Gateway secrets
    RazorpaySecrets: "staging/vendorgateway/razorpay-federal-secured-cards-api-key"
    PanValidationSecretskey: "staging/vendorgateway/panvalidationsecrets"

    #M2P
    M2PSecrets: "staging/vendorgateway/m2p"
    M2PSecuredCardSecrets: "staging/vendorgateway/m2p/secured"
    # TODO(priyansh) : Get this secret added
    M2PMassUnsecuredCardSecrets: "staging/vendorgateway/m2p/mass-unsecured"
    EpifiM2pRsaPrivateKey: "staging/vendorgateway/epifi-m2p-private-key"
    EpifiM2pRsaPublicKey: "staging/vendorgateway/m2p-public-key"

    # In-house BRE
    InHouseBreBearer: "staging/vendorgateway/inhouse-bre-bearer"

    #Federal
    EpifiFederalPgpPrivateKey: "staging/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "staging/pgp/pgp-federal-dummy-key-for-sim"
    EpifiFederalPgpPassphrase: "staging/pgp/pgp-epifi-key-passphrase-fed-api"
    EpifiFederalUPIPrivateKey: "staging/vendorgateway/upi-xml-signature-v1"
    EpifiFederalUPIFallbackPrivateKey: "staging/vendorgateway/upi-xml-signature"
    SenderCode: "staging/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "staging/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "staging/vg-vn-vgpci/federal-auth-service-access-code"
    ClientId: "staging/vg-vgpci/federal-auth-client-id"
    ClientSecretKey: "staging/vg-vgpci/federal-auth-client-secret-key"
    EpifiFederalCardDataPrivateKeyFallBack: "staging/vg-vngw-vgpci/rsa-federal-card-data"
    EpifiFederalCardDataPrivateKey: "staging/vg-vngw-vgpci/federal-card-data-decryption"

    #Closing Balance params
    ClosingBalanceCredentials: "staging/vendorgateway/federal-closing-balance-secrets"

    GetBalanceCredentialsV1: "staging/vendorgateway/federal-get-balance-v1-secrets"

    GetRemitterDetailsCredentials: "staging/vendorgateway/federal-get-remitter-details-secrets"
    GetRemitterDetailsV1Credentials: "staging/vendorgateway/federal-get-remitter-details-secrets-v1"
    GetBeneficiaryNameDetailsCredentials: "staging/vendorgateway/federal-get-beneficiary-name-details-secrets"
    GetCsisStatusCredentials: "staging/vendorgateway/federal-get-csis-status-secrets"

    #FCM
    FCMServiceAccountCredJson: "staging/vendorgateway/fcm-account-credentials"
    #Sendgrid
    SendGridAPIKey: "staging/vendorgateway/sendgrip-api-key"
    #TLS certs
    SimulatorCert: "staging/vg-vgpci/tls-client-cert-for-sim"
    EpiFiFederalClientSslCert: "staging/vg-vgpci/tls-client-cert-for-federal"
    EpiFiFederalClientSslKey: "staging/vg-vgpci/tls-client-priv-key-for-federal"
    #Freshdesk
    FreshdeskApiKey: "staging/vendorgateway/freshdesk-api-key"
    EpifiTechRiskFreshdeskApiKey: "staging/vendorgateway/risk-freshdesk-api-key"
    #Ozonetel
    OzonetelApiKey: "staging/vendorgateway/ozonetel-api-key"
    #Freshchat
    FreshchatApiKey: "staging/vendorgateway/freshchat-api-key"
    # Pan Validation
    PanValidationAccessId: "staging/vendorgateway/federal-auth-pan-validation-access-id"
    PanValidationAccessCode: "staging/vendorgateway/federal-auth-pan-validation-access-code"

    #Loylty
    LoyltyClientId: "staging/vendorgateway/loylty-auth-client-id"
    LoyltyClientKey: "staging/vendorgateway/loylty-auth-client-key"
    LoyltyClientSecret: "staging/vendorgateway/loylty-auth-client-secret"
    LoyltyClientEncryptionKey: "staging/vendorgateway/loylty-auth-client-encryption-key"
    LoyltyEGVModuleId: "staging/vendorgateway/loylty-auth-egv-module-id"
    LoyltyCharityModuleId: "staging/vendorgateway/loylty-auth-charity-module-id"
    LoyltyApplicationId: "staging/vendorgateway/loylty-auth-application-id"
    LoyltyProgramId: "staging/vendorgateway/loylty-auth-program-id"

    #Qwikcilver
    QwikcilverSecrets: "staging/vendorgateway/qwikcilver-secrets"

    #Thriwe
    ThriweSecrets: "staging/vendorgateway/thriwe-secrets"

    #Riskcovry
    RiskcovrySecrets: "staging/vendorgateway/riskcovry-secrets"

    #Onsurity Secrets
    OnsuritySecrets: "staging/vendorgateway/onsurity-secrets"

    # UPI API
    UPISenderUserId: "staging/vendorgateway/federal-upi-sender-user-id"
    UPISenderPassword: "staging/vendorgateway/federal-upi-sender-password"
    UPISenderCode: "staging/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdfede: "staging/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordfede: "staging/vendorgateway/federal-upi-sender-password"
    UPISenderCodefede: "staging/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdIosAddFunds: "staging/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordIosAddFunds: "staging/vendorgateway/federal-upi-sender-password"
    UPISenderCodeIosAddFunds: "staging/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdfifederal: "staging/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordfifederal: "staging/vendorgateway/federal-upi-sender-password"
    UPISenderCodefifederal: "staging/vendorgateway/federal-upi-sender-code"

    # SMS API keys
    TwilioAccountSid: "staging/vendorgateway/twilio-account-sid"
    TwilioApiKey: "staging/vendorgateway/twilio-api-key"
    ExotelApiKey: "staging/vendorgateway/exotel-api-key"
    ExotelApiToken: "staging/vendorgateway/exotel-api-token"
    AclEpifiUserId: "staging/vendorgateway/acl-epifi-user-id"
    AclEpifiPassword: "staging/vendorgateway/acl-epifi-password"
    AclFederalUserId: "staging/vendorgateway/acl-federal-user-id"
    AclFederalPassword: "staging/vendorgateway/acl-federal-password"
    AclEpifiOtpUserId: "staging/vendorgateway/acl-epifi-otp-user-id"
    AclEpifiOtpPassword: "staging/vendorgateway/acl-epifi-otp-password"
    KaleyraFederalApiKey: "staging/vendorgateway/kaleyra-federal-api-key"
    KaleyraFederalCreditCardApiKey: "staging/vendorgateway/kaleyra-federal-cc-api-key"
    KaleyraEpifiApiKey: "staging/vendorgateway/kaleyra-epifi-api-key"
    # KaleyraEpifiNRApiKey: "staging/vendorgateway/kaleyra-epifi-nr-api-key" - uncomment when staging key is created.
    AclWhatsappUserId: "staging/vendorgateway/acl-whatsapp-user-id"
    AclWhatsappPassword: "staging/vendorgateway/acl-whatsapp-password"
    WhatsappEnterpriseId: "staging/vendorgateway/whatsapp-enterprise-id"
    WhatsappEnterpriseToken: "staging/vendorgateway/whatsapp-enterprise-token"
    # Video kyc KARZA api key
    KarzaVkycApiKey: "staging/vendorgateway/karza-vkyc-apikey"
    KarzaVkycPriorityApiKey: "staging/vendorgateway/karza-vkyc-priority-apikey"
    KarzaReVkycPriorityApiKey: "staging/vendorgateway/karza-re-vkyc-priority-apikey"
    KarzaSecrets: "staging/vendorgateway/karza"

    # Rounza sports api's project and api key
    RounazCricketProjectKey: "staging/vendorgateway/roanuz-project-key"
    RounazCricketApiKey: "staging/vendorgateway/roanuz-api-key"

    # Rounaz football api's access and secret key
    RounazFootballAccessKey: "staging/vendorgateway/roanuz-football-access-key"
    RounazFootballSecretKey: "staging/vendorgateway/roanuz-football-secret-key"

    # B2C payments keys
    B2cUserId: "staging/vendorgateway/federal-auth-b2c-payment-user-id"
    B2cPassword: "staging/vendorgateway/federal-auth-b2c-payment-password"
    B2cSenderCodeKey: "staging/vendorgateway/federal-auth-b2c-payment-sender-code"

    # ipstack access key
    IpstackAccessKey: "staging/vendorgateway/ipstack-access-key"

    # Shipway username and password
    ShipwayUsername: "staging/vendorgateway/shipway-username"
    ShipwayPassword: "staging/vendorgateway/shipway-password"

    # client api key for aa
    AaVgSecretsV1: "staging/vendorgateway/aa-sahamati-secrets-v1"
    AaVgVnSecretsV1: "staging/vg-vn/aa-secrets-v1"

    # Experian Credit Report
    ExperianCreditReportPresenceClientName: "staging/vendorgateway/experian-credit-report-presence-client-name"
    ExperianCreditReportFetchClientName: "staging/vendorgateway/experian-credit-report-fetch-client-name"
    ExperianCreditReportForExistingUserClientName: "staging/vendorgateway/experian-credit-report-for-existing-user-client-name"
    ExperianExtendSubscriptionClientName: "staging/vendorgateway/experian-extend-subscription-client-name"
    ExperianVoucherCode: "staging/vendorgateway/experian-credit-report-voucher-code"

    # cvl secrets
    CvlSftpUser: "staging/vendorgateway/cvl-sftp-user"
    CvlSftpPass: "staging/vendorgateway/cvl-sftp-pass"
    CvlSftpUploadUser: "staging/vendorgateway/cvl-sftp-upload-user"
    CvlSftpUploadPass: "staging/vendorgateway/cvl-sftp-upload-pass"
    CvlKraPassKey: "staging/vendorgateway/cvl-kra-pass-key"
    CvlKraPosCode: "staging/vendorgateway/cvl-kra-pos-code"
    CvlKraUserName: "staging/vendorgateway/cvl-kra-user-name"
    CvlKraPassword: "staging/vendorgateway/cvl-kra-password"
    CvlSftpSshKey: "staging/vendorgateway/cvl-sftp-ssh-key"
    CvlSecrets: "staging/vendorgateway/cvl-secrets"

    # nsdl secrets
    NsdlUserId: "staging/vendorgateway/nsdl-user-id"

    # digio secrets
    DigioClientId: "staging/vendorgateway/digio-client-id"
    DigioSecretKey: "staging/vendorgateway/digio-secret-key"

    # Manch secrets
    ManchSecureKey: "staging/vendorgateway/manch-secure-key"
    ManchTemplateKey: "staging/vendorgateway/manch-template-key"

    SeonClientApiKey: "staging/vendorgateway/seon-api-key"

    CAMSKey: "staging/investment-vendorgateway/cams-key"

    KarvyKey: "staging/investment-vendorgateway/karvy-key"

    SmallCaseKey: "staging/vendorgateway/smallcase-key"

    MFCentralKey: "staging/vendorgateway/mfcentral-key"


    #ckyc
    CkycFiCode: "staging/vendorgateway/ckyc-fi-code"

    #digilocker
    DigilockerClientSecret: "staging/vendorgateway/digilocker-client-secret"

    # p2p Investment secrets
    p2pInvestmentLiquiloansSftpUser: "staging/vendorgateway/p2p-investment-liquiloans-sftp-user"
    p2pInvestmentLiquiloansSftpPassword: "staging/vendorgateway/p2p-investment-liquiloans-sftp-pass"

    # TODO(@prasoon): Add secret keys
    # Lending keys
    PreApprovedLoanFederalSecrets: "staging/vendorgateway/lending-preapprovedloans-secrets"
    FederalSftpSshKey: "staging/vendorgateway/federal-sftp-ssh-key"
    PreApprovedLoanSecrets: "staging/vendorgateway/lending-preapprovedloans-secrets"
    FiIdfcPreApprovedLoanPrivateKey: "staging/vendorgateway/fi-idfc-pre-approved-loan-private-key"

    # Leegality
    LeegalitySecret: "staging/vendorgateway/esign-leegality-secrets"

    #Liquiloans
    LiquiloansMid: "staging/vendorgateway/liquiloans-mid"
    LiquiloansKey: "staging/vendorgateway/liquiloans-key"
    LiquiloansSecrets: "staging/vendorgateway/liquiloans-secrets"

    #GPlace api key
    GPlaceApiKey: "staging/vendorgateway/gplace-api-key"

    # karza api keys
    KarzaKey: "staging/vendorgateway/karza-key"
    TartanKey: "staging/vendorgateway/tartan-key"
    VKYCAgentDashboardSecrets: "staging/vendorgateway/vkyc-agent-dash"

    # DronaPay
    DronaPayKey: "staging/vendorgateway/dronapay-key"

    GeolocationKey: "staging/vendorgateway/geolocation-key"

    # Secrets of payu
    PayuToken: "staging/vendorgateway/payu-token"
    PayuApiKey: "staging/vendorgateway/payu-key"

    MaxmindSecrets: "staging/vendorgateway/maxmind-secrets"

    BureauSecrets: "staging/vendorgateway/bureau-secrets"

    SignzySecrets: 'staging/vendorgateway/signzy-secrets'

    AlpacaSecrets: "staging/vendorgateway/alpaca-secrets"

    FederalInternationalFundTransferSecrets: "staging/vendorgateway/federal-internationalfundtransfer-secrets"

    FederalProfileValidationSecrets: 'staging/vendorgateway/hunter-secrets'

    MorningStarSecrets: "staging/vendorgateway/morningstar-secrets"
    MorningStarAccountSecrets: "staging/vendorgateway/morningstar-account-secrets"

    DepositInterestRateInfoSecrets: "staging/vendorgateway/deposit-interest-rate-info-secrets"

    TssApiToken: "staging/vendorgateway/tss-api-token"
    TSSAPITokenForSG: "staging/vendorgateway/tss-api-token-sg"
    TSSCloudCredentials: "staging/vendorgateway/aml/tss-cloud-credentials"

    VistaraSecrets: "staging/vendorgateway/vistara-secrets"

    FederalDepositSecrets: "staging/vendorgateway/federal-deposit-secrets"

    # Fennel Secrets
    FennelFeatureStoreSecrets: "staging/vendorgateway/fennel-secrets"

    SlackSecrets: "staging/vendorgateway/slack-bot-tokens"

    DreamfolksSecrets: "staging/vendorgateway/dreamfolks-secrets"

    CommonSftpUser: "staging/vendorgateway-simulator/sftp-upload-user"
    CommonSftpPass: "staging/vendorgateway-simulator/sftp-upload-pass"

    # Lentra secrets
    LentraSecrets: "staging/vendorgateway/lentra-secrets"

    EpifiFederalEpanSftpSecrets: "staging/vendorgateway/epifi-federal-epan-sftp-secrets"

    EpifiFederalEnachSftpSecrets: "staging/vendorgateway/epifi-federal-enach-sftp-secrets"
    # Credgenics auth token
    CredgenicsAuthToken: "staging/vendorgateway/credgenics"

    CredgenicsAuthenticationKeyV2: "staging/vendorgateway/credgenics-v2"

    LendingMFCentralSecrets: 'staging/vendorgateway/lamf-secrets'

    LeadSquaredSecrets: "staging/vendorgateway/leadsquared-keys"

    LendingFiftyFinLamfSecrets: "staging/vendorgateway/fiftyfin-lamf-secrets"

    KarzaPanProfileKey: "staging/vendorgateway/pan-profile-karza-key"

    PoshvineSecrets: "staging/vendorgateway/poshvine-secrets"

    CibilSecrets: "staging/vendorgateway/cibil"

    AclSftpSecretKey: "staging/sftp/vendors/kaleyra/password"

    BureauIdSecrets: "staging/vendorgateway/bureauid-secrets"

    NetCoreEpifiSecrets: "staging/vendorgateway/netcore-epifi-secrets"

    VisaSecrets: "staging/vendorgateway/visa-secrets"

    MoEngageSecrets: "staging/vendorgateway/moengage-secrets"

    ScienapticSecrets: "staging/vendorgateway/openresty-scienaptic-sms"

    PerfiosDigilockerSecrets: "staging/vendorgateway/perfios-digilocker-secrets"

    AirtelFedSMSSecrets: "staging/vendorgateway/airtel-fed-sms-secrets"

    AirtelEpifiSMSSecrets: "staging/vendorgateway/airtel-epifi-sms-secrets"

    BridgewiseSecrets: "staging/vendorgateway/bridgewise-secrets"

    FederalEscalationSecrets: "staging/vendorgateway/federal-io-secrets"
    FederalEscalationClientSslCert: "staging/vendorgateway/federal-io-cert"
    FederalEscalationClientSslKey: "staging/vendorgateway/federal-io-key"

    SavenSecrets: "staging/vendorgateway/saven-cc-secrets"

    SetuBillPaySecrets: "staging/vendorgateway/setu-billpay-secrets"
    SetuMobileRechargeSecrets: "staging/vendorgateway/setu-mobile-recharge-secrets"

    NuggetSecrets: "staging/vendorgateway/nugget-secrets"

SyncWrapperSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vn-sync-wrapper-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 2
      MaxAttempts: 6
      TimeUnit: "Second"

DisputeSFTP:
  user: ""
  password: ""
  host: ""
  port: 80
  S3BucketName: "epifi-federal-disputes"

Flags:
  TrimDebugMessageFromStatus: false
  TokenValidation: true
  AllowSpecialCharactersInAddress: true
  EnableTransactionEnquiryNewApi: true
  EnableFennelClusterV3: true
  EnableInstrumentBillingInterceptor: true
  EnableCibilV2Secrets: true

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendorgateway/secure.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 15

FcmAnalyticsLabel: "staging-push-notification"

AwsSes:
  FiMoneyArn: "arn:aws:ses:ap-south-1:632884248997:identity/fi.money"
  FiCareArn: "arn:aws:ses:ap-south-1:632884248997:identity/fi.care"
  StockGuardianArn: "arn:aws:ses:ap-south-1:632884248997:identity/stockguardian.in"

# generic downtime handler for vg
# the vendor name is the enum value with config values like IsEnabled, StartTime, EndTime
DowntimeConfig:
  NSDL:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-03-15 15:04:05"
        EndTimestamp: "2022-03-16 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CVLKRA:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CKYC:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-03-15 15:04:0"
        EndTimestamp: "2022-08-16 12:00:00"
        Msg: "This is due to a downtime at our vendor partner. This should be up by %s. We will notify you once its up."
  DIGILOCKER:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  MANCH:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: true
        StartTimestamp: "2022-09-26 11:00:00"
        EndTimestamp: "2022-09-26 11:20:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

DisputeConfig:
  FiRequesterId: "F768484C-9C9A-4023-B298-3BA45DA1F352"

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "staging/pgp/v1/pgp-epifi-fed-api"
  ExternalEntity:
    - Secret: "staging/pgp/v1/federal-simulator"


RedactedRawRequestLogExceptionList: [
  "https://fortuna-ds.data-dev.pointz.in/income_fetch",
]

VideoSdk:
  Secrets:
    Path: "staging/vendorgateway/videosdk"

FederalAPICreds:
  REQUEST_SOURCE_UNSPECIFIED:
    Path: "staging/vg-vgpci/vendor-api-secrets-federal-default"
  REQUEST_SOURCE_LOANS:
    Path: "staging/vg-vgpci/vendor-api-secrets-federal-b2c-loans"
  REQUEST_SOURCE_BILLPAY_RECHARGE:
    Path: "staging/vg-vgpci/vendor-api-secrets-federal-default"

Uqudo:
  AccessTokenURL: "https://auth.uqudo.io/api/oauth/token"
  PublicKeyURL: "https://id.uqudo.io/api/.well-known/jwks.json"
  FetchImageURL: "https://id.uqudo.io/api/v1/info/img"
  EmiratesIdOcrUrl: "https://id.uqudo.io/api/v1/scan"
  Secrets:
    Path: "staging/vendorgateway/uqudo"
