package aml

import (
	"context"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/vendorgateway/config"
)

type getCaseDetailsReq struct {
	req        *amlVgPb.GetCaseDetailsRequest
	tenantConf *config.TSSCloudTenant
}

func (s *getCaseDetailsReq) URL() string {
	return s.tenantConf.URL + "/screeningactionable/as503"
}

func (s *getCaseDetailsReq) HTTPMethod() string {
	return http.MethodPost
}

func (s *getCaseDetailsReq) Add(req *http.Request) *http.Request {
	req.Header.Add("Cluster", "CL1_User")
	req.Header.Add("Domain", s.URL())
	req.Header.Add("ApiToken", s.tenantConf.AS503APIToken)
	return req
}

func (s *getCaseDetailsReq) GetResponse() vendorapi.Response {
	return &getCaseDetailsRes{}
}

func (s *getCaseDetailsReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (s *getCaseDetailsReq) Marshal() ([]byte, error) {
	if s.req.GetCaseId() == "" {
		return nil, errors.New("case ID is mandatory")
	}
	tssReq := &tss.GetCaseDetailsRequest{
		RequestId:     idgen.RandSeqDigitsWithoutLeadingZeroes(vendorRequestIdLen),
		ApiRequestDto: &tss.GetCaseDetailsRequestData{CaseId: s.req.GetCaseId()},
	}
	reqJson, err := protojson.Marshal(tssReq)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling request to JSON")
	}
	return reqJson, nil
}

func (s *getCaseDetailsReq) RedactRequestBody(_ context.Context, reqBody []byte, _ string) ([]byte, error) {
	return reqBody, nil
}

type getCaseDetailsRes struct{}

func (s *getCaseDetailsRes) UnmarshalV2(_ context.Context, b []byte) (proto.Message, error) {
	tssCaseDetailsRes := &tss.GetCaseDetailsResponse{}
	err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, tssCaseDetailsRes)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response to TSS proto")
	}
	vgCaseDetailsRes, err := s.convertTSSResToVGRes(tssCaseDetailsRes)
	if err != nil {
		return nil, errors.Wrap(err, "error converting TSS response to VG proto")
	}
	return vgCaseDetailsRes, nil
}

func (s *getCaseDetailsRes) Unmarshal(b []byte) (proto.Message, error) {
	return s.UnmarshalV2(context.Background(), b)
}

func (s *getCaseDetailsRes) convertTSSResToVGRes(res *tss.GetCaseDetailsResponse) (*amlVgPb.GetCaseDetailsResponse, error) {
	if res.GetValidationCodes() != "" || res.GetValidationDescriptions() != "" {
		return &amlVgPb.GetCaseDetailsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("validation codes: %s, validation descriptions: %s",
				res.GetValidationCodes(), res.GetValidationDescriptions())),
			RequestId: res.GetRequestId(),
		}, nil
	}
	caseDetails, err := convertTSSCaseToVGCase(res.GetResponseData().GetCaseDetails())
	if err != nil {
		return nil, errors.Wrap(err, "error converting TSS case to VG case")
	}
	return &amlVgPb.GetCaseDetailsResponse{
		Status:    rpc.StatusOk(),
		RequestId: res.GetRequestId(),
		Case:      caseDetails,
	}, nil
}

func (s *getCaseDetailsRes) RedactResponseBody(ctx context.Context, responseBody []byte, contentType string) ([]byte, error) {
	return httpcontentredactor.GetInstance().Redact(ctx, responseBody, contentType, map[string]mask.MaskingStrategy{
		"customerName":                     mask.DontMaskFirstTwoAndLastTwoChars,
		"sourceSystemCustomerCode":         mask.DontMaskLastFourChars,
		"linkedToSourceSystemCustomerCode": mask.DontMaskLastFourChars,
		"applicationRefNumber":             mask.DontMaskLastFourChars,
		"userName":                         mask.DontMaskFirstTwoAndLastTwoChars,
		"comment":                          mask.DontMaskFirstTwoAndLastTwoChars,
		"reportDataInBase64String":         mask.MaskToStaticValue,
	})
}

func (s *getCaseDetailsRes) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
