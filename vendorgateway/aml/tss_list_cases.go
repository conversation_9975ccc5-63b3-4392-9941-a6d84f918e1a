package aml

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/vendorgateway/config"
)

type listCasesReq struct {
	req        *amlVgPb.ListCasesRequest
	tenantConf *config.TSSCloudTenant
}

func (s *listCasesReq) URL() string {
	return s.tenantConf.URL + "/screeningactionable/as502"
}

func (s *listCasesReq) HTTPMethod() string {
	return http.MethodPost
}

func (s *listCasesReq) Add(req *http.Request) *http.Request {
	req.Header.Add("Cluster", "CL1_User")
	req.Header.Add("Domain", s.URL())
	req.Header.Add("ApiToken", s.tenantConf.AS502APIToken)
	return req
}

func (s *listCasesReq) GetResponse() vendorapi.Response {
	return &listCasesRes{}
}

func (s *listCasesReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (s *listCasesReq) Marshal() ([]byte, error) {
	// Convert case categories to comma-separated string
	caseCategoriesStr := convertCaseCategoriesToString(s.req.GetCaseCategories())
	if caseCategoriesStr == "" {
		return nil, errors.New("case categories are mandatory")
	}

	// Convert case types to comma-separated string
	caseTypesStr := convertCaseTypesToString(s.req.GetCaseTypes())
	if caseTypesStr == "" {
		return nil, errors.New("case types are mandatory")
	}

	// Convert timestamps to ISO 8601 format and validate
	fromDateTime := s.req.GetFromDateTime().AsTime().Format(time.RFC3339)
	toDateTime := s.req.GetToDateTime().AsTime().Format(time.RFC3339)
	if fromDateTime == toDateTime {
		return nil, errors.New("from and to date time cannot be the same")
	}
	if s.req.GetFromDateTime().AsTime().After(s.req.GetToDateTime().AsTime()) {
		return nil, errors.New("from date time cannot be after to date time")
	}
	if s.req.GetToDateTime().AsTime().Sub(s.req.GetFromDateTime().AsTime()) > 30*24*time.Hour {
		return nil, errors.New("from and to date time cannot be more than 30 days apart")
	}

	// Create the TSS request
	tssReq := &tss.ListCasesRequest{
		RequestId: fmt.Sprintf("list-cases-%d", time.Now().Unix()),
		ApiRequestDto: &tss.ListCasesRequestQuery{
			CaseCategory:     caseCategoriesStr,
			SourceSystemName: s.tenantConf.Name,
			FromDateTime:     fromDateTime,
			ToDateTime:       toDateTime,
			CaseType:         caseTypesStr,
		},
	}

	reqJson, err := protojson.Marshal(tssReq)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling request to JSON")
	}
	return reqJson, nil
}

func convertCaseCategoriesToString(categories []amlVgPb.CaseCategory) string {
	var categoryStrings []string
	for _, category := range categories {
		switch category {
		case amlVgPb.CaseCategory_CASE_CATEGORY_OPEN:
			categoryStrings = append(categoryStrings, "Open")
		case amlVgPb.CaseCategory_CASE_CATEGORY_PENDING:
			categoryStrings = append(categoryStrings, "Pending")
		case amlVgPb.CaseCategory_CASE_CATEGORY_COMPLETED:
			categoryStrings = append(categoryStrings, "Completed")
		}
	}
	return strings.Join(categoryStrings, ",")
}

func convertCaseTypesToString(caseTypes []amlVgPb.CaseType) string {
	var typeStrings []string
	for _, caseType := range caseTypes {
		switch caseType {
		case amlVgPb.CaseType_CASE_TYPE_INITIAL:
			typeStrings = append(typeStrings, "Initial")
		case amlVgPb.CaseType_CASE_TYPE_WATCHLIST_ADDED:
			typeStrings = append(typeStrings, "WatchlistAdded")
		}
	}
	return strings.Join(typeStrings, ",")
}

func (s *listCasesReq) RedactRequestBody(ctx context.Context, requestBody []byte, _ string) ([]byte, error) {
	return requestBody, nil
}

type listCasesRes struct{}

func (s *listCasesRes) UnmarshalV2(_ context.Context, b []byte) (proto.Message, error) {
	res := &tss.ListCasesResponse{}
	err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, res)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response to TSS proto")
	}
	vgRes, err := s.convertTSSResToVGRes(res)
	if err != nil {
		return nil, errors.Wrap(err, "error converting TSS response to VG proto")
	}
	return vgRes, nil
}

func (s *listCasesRes) Unmarshal(b []byte) (proto.Message, error) {
	return s.UnmarshalV2(context.Background(), b)
}

func (s *listCasesRes) convertTSSResToVGRes(res *tss.ListCasesResponse) (*amlVgPb.ListCasesResponse, error) {
	if res.GetValidationCodes() != "" || res.GetValidationDescriptions() != "" {
		return &amlVgPb.ListCasesResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("validation codes: %s, validation descriptions: %s",
				res.GetValidationCodes(), res.GetValidationDescriptions())),
			RequestId: res.GetRequestId(),
		}, nil
	}

	if int(res.GetResponseData().GetCaseCount()) != len(res.GetResponseData().GetCaseDetails()) {
		return nil, errors.New("case count does not match number of case details")
	}

	if len(res.GetResponseData().GetCaseDetails()) == 0 {
		return &amlVgPb.ListCasesResponse{
			Status:    rpc.StatusRecordNotFound(),
			RequestId: res.GetRequestId(),
		}, nil
	}

	var cases []*amlVgPb.Case
	for _, tssCase := range res.GetResponseData().GetCaseDetails() {
		vgCase, err := convertTSSCaseToVGCase(tssCase)
		if err != nil {
			return nil, errors.Wrap(err, "error converting TSS case to VG case")
		}
		cases = append(cases, vgCase)
	}
	return &amlVgPb.ListCasesResponse{
		Status:    rpc.StatusOk(),
		RequestId: res.GetRequestId(),
		Cases:     cases,
	}, nil
}

func (s *listCasesRes) RedactResponseBody(ctx context.Context, responseBody []byte, contentType string) ([]byte, error) {
	return httpcontentredactor.GetInstance().Redact(ctx, responseBody, contentType, map[string]mask.MaskingStrategy{
		"customerName":                     mask.DontMaskFirstTwoAndLastTwoChars,
		"sourceSystemCustomerCode":         mask.DontMaskLastFourChars,
		"linkedToSourceSystemCustomerCode": mask.DontMaskLastFourChars,
		"applicationRefNumber":             mask.DontMaskLastFourChars,
	})
}

func (s *listCasesRes) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
