package storage

import (
	"context"
	"crypto/tls"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/url"
	"path/filepath"
	"strconv"
	"time"

	"github.com/dlmiddlecote/sqlstats"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/postgres"
	"github.com/mohae/deepcopy"
	"github.com/prometheus/client_golang/prometheus"
	redisPrometheusV9 "github.com/redis/go-redis/extra/redisprometheus/v9"
	"github.com/redis/go-redis/v9"
	_ "github.com/snowflakedb/gosnowflake"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	awspkg "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/file"
	"github.com/epifi/be-common/pkg/logger"
	pkgRedisProm "github.com/epifi/be-common/pkg/redisprom"
	smpkgv2 "github.com/epifi/be-common/pkg/secrets/aws"
	"github.com/epifi/be-common/pkg/storage/metrics"
	pkgTracing "github.com/epifi/be-common/pkg/tracing/opentelemetry"
)

const (
	defaultCertDirCRDBSecureCluster = "./crdb/"
	defaultCertDirPGDBSecureCluster = "./pgdb/"
	defaultRedisCmdTimeout          = 100 * time.Millisecond
)

// Deprecated: use storage/v2.NewCRDBWithConfig for CRDB and storage/v2.NewPostgresDBWithConfig for pgdb
func NewSQLDBWithConfig(crdbConfig *cfg.DB) (*sql.DB, error) {
	connString, err := GetCRDBConnURL(crdbConfig)
	if err != nil {
		return nil, err
	}

	db, err := sql.Open(PostgresDriver, connString)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to crdb: %s on %s:%d: %w",
			crdbConfig.GetName(), crdbConfig.Host, crdbConfig.Port, err)
	}
	return db, nil
}

// NewGormDB returns a sql.DB database handle. It is the responsibility of the caller
// to close it after use.
// For backward compatability, creating a new method which'll use cfg
//
// For secure cluster: The standard terminology used here denotes:
// sslrootcert: digital certificate of the CA containing CA's public key
// sslcert: a digital certificate for the client. It contains the client's public key.
// sslkey: private key for the client
// TODO(kunal): Remove code duplication after all services have migrated to this method
// Deprecated: use storage/v2.NewCRDBWithConfig for CRDB and storage/v2.NewPostgresDBWithConfig for pgdb
func NewGormDBWithConfig(crdbConfig *cfg.DB) (*gorm.DB, error) {
	rootCertFilePath := GetCRDBSslRootCertPath(crdbConfig.SSLCertPath)
	clientKeyPath := GetCRDBSslClientKeyPath(crdbConfig.SSLCertPath, crdbConfig.Username)
	clientCertPath := GetCRDBSslClientCertPath(crdbConfig.SSLCertPath, crdbConfig.Username)

	connString, err := GetCRDBConnURL(crdbConfig)
	if err != nil {
		return nil, err
	}

	if crdbConfig.SSLMode == DBSSLModeVerifyFull {
		err = CreateSslFiles(crdbConfig, rootCertFilePath, clientKeyPath, clientCertPath)
		if err != nil {
			return nil, err
		}
	}

	logger.DebugNoCtx(connString)
	db, err := gorm.Open(PostgresDriver, connString)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to crdb: %s on %s:%d: %w",
			crdbConfig.GetName(), crdbConfig.Host, crdbConfig.Port, err)
	}
	db, err = setGormV1Logger(crdbConfig, db)
	if err != nil {
		return nil, fmt.Errorf("failed to set logger: %w", err)
	}
	if crdbConfig.MaxOpenConn != 0 {
		db.DB().SetMaxOpenConns(crdbConfig.MaxOpenConn)
	}

	if crdbConfig.MaxIdleConn != 0 {
		db.DB().SetMaxIdleConns(crdbConfig.MaxIdleConn)
	}

	if crdbConfig.MaxConnTtl != 0 {
		db.DB().SetConnMaxLifetime(crdbConfig.MaxConnTtl)
	}

	// Create a new collector, the name will be used as a label on the metrics
	collector := sqlstats.NewStatsCollector(crdbConfig.GetName(), db.DB())
	// Register it with Prometheus and return error in case registration fails
	// ignore Err in-case of duplicate registration
	if err = prometheus.Register(collector); err != nil {
		if ok := errors.As(err, &prometheus.AlreadyRegisteredError{}); !ok {
			return nil, fmt.Errorf("failed to register stats collector: %w", err)
		}
	}

	return db, nil
}

func setGormV1Logger(config *cfg.DB, db *gorm.DB) (*gorm.DB, error) {
	gormConf := config.GormV1
	if gormConf != nil {
		switch {
		case gormConf.LogMode == cfg.DETAILED_SQL_LOG_MODE:
			db = db.LogMode(true)
		case gormConf.LogMode == cfg.NO_SQL_LOG_MODE:
			db = db.LogMode(false)
		default: // don't set LogMode to use in default log mode. gorm has three modes of logging but expose API through boolean flag.
			// Not setting the log mode is the only way to use default log mode which logs only SQL errors
		}
		if gormConf.UseInsecureLog {
			db.SetLogger(NewGormLogger(logger.GetLogNoCtxFunc(cfg.GetLogLevel(gormConf.LogLevel))))
		} else {
			env, err := cfg.GetEnvironment()
			if err != nil {
				return nil, err
			}
			if cfg.IsProdEnv(env) {
				logLevel := cfg.GetLogLevel(gormConf.LogLevel)
				logFunc := func(msg string, fields ...zap.Field) {
					logger.LogSecure(logLevel, context.Background(), msg, fields...)
				}
				db.SetLogger(NewGormLogger(logFunc))
			} else {
				db.SetLogger(NewGormLogger(logger.GetLogNoCtxFunc(cfg.GetLogLevel(gormConf.LogLevel))))
			}
		}

	} else {
		db = db.LogMode(config.EnableDebug)
		if !logger.IsDevLogger {
			db.SetLogger(NewGormLogger(logger.GetLogNoCtxFunc(zapcore.DebugLevel)))
		}
	}
	return db, nil
}

// NewPostgresDBWithConfig creates connection string from DB config, opens connection, setup logger and stats collector
func NewPostgresDBWithConfig(rdsConfig *cfg.DB) (*gorm.DB, error) {
	connString, err := FormPostgresConnString(rdsConfig)
	if err != nil {
		return nil, err
	}
	db, err := gorm.Open("postgres", connString)
	if err != nil {
		return nil, err
	}
	db, err = setGormV1Logger(rdsConfig, db)
	if err != nil {
		return nil, fmt.Errorf("failed to set logger: %w", err)
	}
	if rdsConfig.MaxOpenConn != 0 {
		db.DB().SetMaxOpenConns(rdsConfig.MaxOpenConn)
	}

	if rdsConfig.MaxIdleConn != 0 {
		db.DB().SetMaxIdleConns(rdsConfig.MaxIdleConn)
	}

	if rdsConfig.MaxConnTtl != 0 {
		db.DB().SetConnMaxLifetime(rdsConfig.MaxConnTtl)
	}
	err = SetupStatsCollectorForSQLDB(rdsConfig, db.DB())
	if err != nil {
		return nil, err
	}
	return db, nil
}

func SetupStatsCollectorForSQLDB(rdsConfig *cfg.DB, db *sql.DB) error {
	// Create a new collector, the name will be used as a label on the metrics
	collector := sqlstats.NewStatsCollector(rdsConfig.GetName(), db)
	// Register it with Prometheus and return error in case registration fails
	// ignore Err in-case of duplicate registration
	if err := prometheus.Register(collector); err != nil {
		if ok := errors.As(err, &prometheus.AlreadyRegisteredError{}); !ok {
			return fmt.Errorf("failed to register stats collector: %w", err)
		}
	}
	return nil
}

// Deprecated: use storage/v2.GetPgDbDsnString
// WARNING! Backward in-compatible change in storage/v2.GetPgDbDsnString when porting to new method please make sure you
// call CreateSslFilesForPGDBConn which isn't called inside the new implementation
func FormPostgresConnString(rdsConfig *cfg.DB) (string, error) {
	var connString string
	sslMode := rdsConfig.SSLMode
	switch {
	case sslMode == "disable" || sslMode == "require":
		connString = fmt.Sprintf("postgresql://%s:%s@%s:%d/%s?sslmode=%s",
			rdsConfig.Username, rdsConfig.Password, rdsConfig.Host, rdsConfig.Port, rdsConfig.GetName(), rdsConfig.SSLMode)
	case sslMode == "verify-full":
		rootCertFilePath := GetPGDBSslRootCertPath(rdsConfig.SSLCertPath)
		err := CreateSslFilesForPGDBConn(rdsConfig, rootCertFilePath)
		if err != nil {
			return "", err
		}
		connString = fmt.Sprintf("postgresql://%s:%s@%s:%d/%s?sslmode=%s&sslrootcert=%s",
			rdsConfig.Username, rdsConfig.Password, rdsConfig.Host, rdsConfig.Port, rdsConfig.GetName(), rdsConfig.SSLMode, rootCertFilePath)
	default:
		return "", fmt.Errorf("failed to connect to pgdb: unsupported SSLMode specified %s", sslMode)
	}

	stmtTimeout := rdsConfig.StatementTimeout

	if stmtTimeout == 0 {
		stmtTimeout = DefaultStatementTimeout
	}

	idleTxnTimeout := rdsConfig.IdleInTxnSessionTimeout

	if idleTxnTimeout == 0 {
		idleTxnTimeout = DefaultIdleInTxnSessionTimeout
	}

	connString = fmt.Sprintf("%s&statement_timeout=%d", connString, stmtTimeout.Milliseconds())
	connString = fmt.Sprintf("%s&idle_in_transaction_session_timeout=%d", connString, idleTxnTimeout.Milliseconds())

	return connString, nil
}

func NewDefaultPostgresConn(rdsConfig *cfg.DB) (*gorm.DB, error) {
	connString := FormDefaultPostgresConnString(rdsConfig)
	db, err := gorm.Open("postgres", connString)
	if err != nil {
		return nil, err
	}
	return db, nil
}

func PgNow() time.Time {
	// postgres supports only microsecond precision.
	// Hence, we round off now() value from nanoseconds to microseconds precision.
	// https://github.com/go-gorm/gorm/issues/3232
	return time.Unix(0, time.Now().UnixNano()/int64(time.Microsecond)*1000)
}

func FormDefaultPostgresConnString(rdsConfig *cfg.DB) string {
	connString := fmt.Sprintf("postgresql://%s:%s@%s:%d/%s?sslmode=%s",
		rdsConfig.Username, rdsConfig.Password, rdsConfig.Host, rdsConfig.Port, "postgres", rdsConfig.SSLMode)
	stmtTimeout := rdsConfig.StatementTimeout

	if stmtTimeout == 0 {
		stmtTimeout = DefaultStatementTimeout
	}

	idleTxnTimeout := rdsConfig.IdleInTxnSessionTimeout

	if idleTxnTimeout == 0 {
		idleTxnTimeout = DefaultIdleInTxnSessionTimeout
	}

	connString = fmt.Sprintf("%s&statement_timeout=%d", connString, stmtTimeout.Milliseconds())
	connString = fmt.Sprintf("%s&idle_in_transaction_session_timeout=%d", connString, idleTxnTimeout.Milliseconds())

	return connString
}

func GetCRDBSslRootCertPath(sslCertPath string) string {
	if sslCertPath == "" {
		sslCertPath = defaultCertDirCRDBSecureCluster
	}
	expectedFileName := "ca.crt"
	return filepath.Join(sslCertPath, expectedFileName)
}

func GetPGDBSslRootCertPath(sslCertPath string) string {
	if sslCertPath == "" {
		sslCertPath = defaultCertDirPGDBSecureCluster
	}
	expectedFileName := "rds-ca.pem"
	return filepath.Join(sslCertPath, expectedFileName)
}

func GetCRDBSslClientCertPath(sslCertPath, username string) string {
	if sslCertPath == "" {
		sslCertPath = defaultCertDirCRDBSecureCluster
	}
	expectedFileName := "client." + username + ".crt"
	return filepath.Join(sslCertPath, expectedFileName)
}

func GetCRDBSslClientKeyPath(sslCertPath, username string) string {
	if sslCertPath == "" {
		sslCertPath = defaultCertDirCRDBSecureCluster
	}
	expectedFileName := "client." + username + ".key"
	return filepath.Join(sslCertPath, expectedFileName)
}

func CreateSslFiles(crdbConfig *cfg.DB, rootCertFilePath, clientKeyPath, clientCertPath string) error {
	err := file.CreateFileWithGivenPermissionAndContent(rootCertFilePath, crdbConfig.GetSSLRootCert(), 0600)
	if err != nil {
		return err
	}
	// IMPORTANT: client key file should have a perm mode <=600
	err = file.CreateFileWithGivenPermissionAndContent(clientKeyPath, crdbConfig.GetSslClientKey(), 0600)
	if err != nil {
		return err
	}
	err = file.CreateFileWithGivenPermissionAndContent(clientCertPath, crdbConfig.GetSslClientCert(), 0600)
	if err != nil {
		return err
	}
	return nil
}

// Deprecated: use storage/v2.CreateSslFilesForPGDBConn instead
func CreateSslFilesForPGDBConn(crdbConfig *cfg.DB, rootCertFilePath string) error {
	err := file.CreateFileWithGivenPermissionAndContent(rootCertFilePath, crdbConfig.GetSSLRootCert(), 0600)
	if err != nil {
		return err
	}
	return nil
}

// Deprecated: use NewRedisClientFromConfig
// NewRedisClient returns a redis.Client handle. it will initialise client with tls config if isSecureRedis flag is
// passed as true
func NewRedisClient(redisOptions *redis.Options, isSecureRedis bool, initTracer bool) *redis.Client {
	ro := &cfg.RedisOptions{
		Options:       redisOptions,
		IsSecureRedis: isSecureRedis,
	}
	return NewRedisClientFromConfig(ro, initTracer)
}

// NewRedisClientFromConfig returns a redis.Client handle. it will initialise client with tls config if isSecureRedis flag is
// passed as true
// Note: if redisOptions.MaxRetries not set (Zero), MaxRetries will be disabled by setting -1
func NewRedisClientFromConfig(redisOptions *cfg.RedisOptions, initTracer bool) *redis.Client {
	// TODO(Sundeep): Eliminate the loading of secrets, once the secrets are being loaded using LoadAllSecretsV3.
	// Set up AWS session and secrets manager if credentials are provided
	if redisOptions.AuthDetails != nil {
		region := redisOptions.AuthDetails.Region
		awsConfig, err := awspkg.NewAWSConfig(context.Background(), region, true)
		if err != nil {
			logger.Fatal("error creating a new AWS config", zap.Error(err))
			return nil
		}
		smClient := smpkgv2.NewAwsSecretsManagerClient(awsConfig)
		sm := smpkgv2.NewAwsSecretManager(smClient)

		secretVal, fetchSecretErr := sm.GetSecret(context.Background(), redisOptions.AuthDetails.SecretPath)
		if fetchSecretErr != nil {
			logger.Fatal("error fetching secret from AWS Secrets Manager", zap.Error(fetchSecretErr))
			return nil
		}

		type Credentials struct {
			Username string
			Password string
		}
		var credentials Credentials
		if marshalErr := json.Unmarshal([]byte(secretVal), &credentials); marshalErr != nil {
			logger.Fatal("Error unmarshalling credentials from AWS Secrets Manager", zap.Error(marshalErr))
			return nil
		}
		redisOptions.Username = credentials.Username
		redisOptions.Password = credentials.Password
	}

	// Set default MaxRetries if not provided
	if redisOptions.MaxRetries == 0 {
		redisOptions.MaxRetries = -1 // Disable retries if not set
	}

	// Enable TLS if configured to use secure Redis
	if redisOptions.IsSecureRedis {
		// nolint:gosec
		redisOptions.TLSConfig = &tls.Config{}
	}

	// Set InsecureSkipVerify for testing or remote debugging
	if cfg.IsTestTenantEnabled() || cfg.IsRemoteDebugEnabled() {
		if redisOptions.TLSConfig == nil {
			// nolint:gosec
			redisOptions.TLSConfig = &tls.Config{}
		}
		redisOptions.TLSConfig.InsecureSkipVerify = true
	}

	// Set client name if provided
	if redisOptions.ClientName != "" {
		redisOptions.Options.OnConnect = func(ctx context.Context, conn *redis.Conn) error {
			return conn.Process(ctx, redis.NewCmd(ctx, "CLIENT", "SETNAME", redisOptions.ClientName))
		}
	}

	// Create Prometheus hook for Redis client metrics
	redisPrometheusHook := pkgRedisProm.NewHook(
		pkgRedisProm.WithInstanceName(redisOptions.ClientName),
		pkgRedisProm.WithDurationBuckets([]float64{.001, 0.003, .005, .008, .01, 0.02, 0.05, 0.1}),
	)

	// Adjust DB for testing environments
	if cfg.IsTestTenantEnabled() {
		redisOptions.Options.DB += 20
	} else if redisOptions.Options.DB >= 20 {
		logger.Fatal("DBs >= 20 are reserved for testing and not allowed without TEST_TENANT environment")
	}

	// Create Redis client
	client := redis.NewClient(redisOptions.Options)

	// Register Prometheus collectors and add Prometheus hook
	_ = prometheus.Register(redisPrometheusV9.NewCollector("", "", client))
	_ = prometheus.Register(metrics.NewRedisCollector(redisOptions.ClientName, client))
	client.AddHook(redisPrometheusHook)

	// Initialize tracing for the client if required
	if initTracer && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() {
		if err := pkgTracing.InstrumentRedisV9ClientTracing(client); err != nil {
			logger.DebugNoCtx("failed to instrument tracing for Redis client", zap.Error(err))
		}
		return client
	}

	return client
}

type RedisMasterReplica struct {
	MasterClient  *redis.Client
	ReplicaClient *redis.Client
}

// Close closes the redis clients
func (r *RedisMasterReplica) Close() {
	r.MasterClient.Close()
	r.ReplicaClient.Close()
}

func (r *RedisMasterReplica) Ping() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if _, err := r.MasterClient.Ping(ctx).Result(); err != nil {
		return err
	}
	if _, err := r.ReplicaClient.Ping(ctx).Result(); err != nil {
		return err
	}
	return nil
}

// getReplicaRedisOption clones the master redis options and replaces Node Address and client name for Replica Redis Options
func getReplicaRedisOption(redisOptions *cfg.RedisOptions) *cfg.RedisOptions {
	readRedisOptions := deepcopy.Copy(redisOptions).(*cfg.RedisOptions)

	replicaNodeOptions := readRedisOptions.Options
	// inject replica address in clones redis options object
	replicaNodeOptions.Addr = readRedisOptions.ReplicaAddr
	if readRedisOptions.ClientName != "" {
		// if master redis options has client name, define a client name for replica
		readRedisOptions.ClientName = readRedisOptions.ClientName + "-replica"
	}
	readRedisOptions.Options = replicaNodeOptions
	return readRedisOptions
}

func NewRedisMasterReplicaFromConfig(redisOptions *cfg.RedisOptions, initTracer bool) (store *RedisMasterReplica, err error) {
	var rs *RedisMasterReplica

	// setup master client
	masterClient := NewRedisClientFromConfig(redisOptions, initTracer)

	// if replica addr is not set, return master client as replica client
	if !redisOptions.EnableReadsFromReplica || redisOptions.ReplicaAddr == "" {
		logger.InfoNoCtx("replica addr not set, reads will be served from master")
		rs = &RedisMasterReplica{
			MasterClient:  masterClient,
			ReplicaClient: masterClient,
		}

		if err = rs.Ping(); err != nil {
			return nil, err
		}
		return rs, nil
	}

	// setup replica client
	replicaRedisOptions := getReplicaRedisOption(redisOptions)
	// todo(Vineet): remove this log
	logger.DebugNoCtx("replica options", zap.String(logger.PAYLOAD, fmt.Sprintf("%v", replicaRedisOptions.Options)))
	logger.DebugNoCtx("master options", zap.String(logger.PAYLOAD, fmt.Sprintf("%v", redisOptions.Options)))
	replicaClient := NewRedisClientFromConfig(replicaRedisOptions, initTracer)

	rs = &RedisMasterReplica{
		MasterClient:  masterClient,
		ReplicaClient: replicaClient,
	}
	if err = rs.Ping(); err != nil {
		return nil, err
	}
	return rs, nil
}

// GetCRDBConnURL generates URL to connect to CRDB
// Deprecated: use storage/v2.GetCrdbDsnString
func GetCRDBConnURL(crdbConfig *cfg.DB) (string, error) {
	queryVal := url.Values{}
	queryVal.Set(DBConnSSLMode, crdbConfig.SSLMode)
	if crdbConfig.AppName != "" {
		queryVal.Set(DBConnAppName, crdbConfig.AppName)
	}
	stmtTimeout := crdbConfig.StatementTimeout
	if stmtTimeout == 0 {
		stmtTimeout = DefaultStatementTimeout
	}
	if stmtTimeout > 0 {
		queryVal.Set(StatementTimeout, fmt.Sprint(stmtTimeout.Milliseconds()))
	}
	idleTxnTimeout := crdbConfig.IdleInTxnSessionTimeout
	if idleTxnTimeout == 0 {
		idleTxnTimeout = DefaultIdleInTxnSessionTimeout
	}
	if idleTxnTimeout > 0 {
		queryVal.Set(IdleInTxnSessionTimeout, fmt.Sprint(idleTxnTimeout.Milliseconds()))
	}
	if crdbConfig.SSLMode == DBSSLModeVerifyFull {
		queryVal.Set(DBConnSSLRootCert, GetCRDBSslRootCertPath(crdbConfig.SSLCertPath))
		queryVal.Set(DBConnSSLKey, GetCRDBSslClientKeyPath(crdbConfig.SSLCertPath, crdbConfig.Username))
		queryVal.Set(DBConnSSLCert, GetCRDBSslClientCertPath(crdbConfig.SSLCertPath, crdbConfig.Username))
	}

	uri := &url.URL{
		Scheme:   PostgresSQLSchema,
		User:     url.User(crdbConfig.Username),
		Host:     net.JoinHostPort(crdbConfig.Host, strconv.Itoa(crdbConfig.Port)),
		Path:     crdbConfig.GetName(),
		RawQuery: queryVal.Encode(),
	}

	return url.QueryUnescape(uri.String())
}

func NewSnowflakeDBFromConfig(snowflakeConfig *cfg.Snowflake) (*sql.DB, error) {
	connString := GetSnowflakeConnURL(snowflakeConfig)
	snowflakeDb, err := sql.Open("snowflake", connString)
	if err != nil {
		return nil, err
	}
	return snowflakeDb, nil
}

func GetSnowflakeConnURL(config *cfg.Snowflake) string {
	return fmt.Sprintf("%s:%s@%s/%s/%s?warehouse=%s", config.Username, config.Password, config.Host, config.DBName, config.SchemaName, config.Warehouse)
}
